package UI.test
{
   import UI.count.CountCtrl;
   import com.greensock.TweenLite;
   import dataAll._player.PlayerDataSupple;
   import dataAll.gift.GiftAddit;
   import flash.display.DisplayObject;
   import flash.display.Graphics;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import utils.ClassProperty;
   import utils.JSON2;
   import utils.ObjectToXml;
   
   public class NewGMConsole extends Sprite
   {
      private var background:Shape;
      
      private var titleText:TextField;
      
      private var outputText:TextField;
      
      private var closeBtn:Sprite;
      
      private var isVisible:Boolean = false;
      
      private var currentCategory:String = "玩家";
      
      private var categoryBtns:Array = [];
      
      private var categoryNames:Array = ["玩家","装备","关卡","物品","系统","存档","任务","宠物","竞技","军团","成就","时间","自定义","其他"];
      
      private var categoryColors:Array = [16737792,6684927,65382,16711782,26367,16750848,10027263,65433,16711833,16737945,6750105,16724838,16716947,10066431];
      
      private var buttonContainer:Sprite;
      
      private var functionBtns:Array = [];
      
      private var inputContainer:Sprite;
      
      private var inputBox:TextField;
      
      private var inputLabel:TextField;
      
      private var inputBtn:Sprite;
      
      private var currentInputCommand:String = "";
      
      public function NewGMConsole()
      {
         super();
         this.createUI();
         this.setupEventListeners();
         this.visible = false;
      }
      
      private function createUI() : void
      {
         background = new Shape();
         var g:Graphics = background.graphics;
         g.beginFill(16773368,0.95);
         g.drawRoundRect(0,0,900,600,25,25);
         g.endFill();
         g.lineStyle(4,16738740,1);
         g.drawRoundRect(0,0,900,600,25,25);
         g.lineStyle(2,8900331,0.8);
         g.drawRoundRect(3,3,894,594,22,22);
         g.lineStyle(1,16766720,0.9);
         g.drawRoundRect(6,6,888,588,19,19);
         drawCuteDecorations(g);
         addChild(background);
         var titleBg:Shape = new Shape();
         var titleG:Graphics = titleBg.graphics;
         titleG.beginFill(16738740,0.9);
         titleG.drawRoundRect(0,0,900,50,25,25);
         titleG.endFill();
         titleG.beginFill(16761035,0.7);
         titleG.drawRect(0,40,900,10);
         titleG.endFill();
         drawStarDecorations(titleG);
         addChild(titleBg);
         titleText = createTextField("✨ GM魔法控制台 ✨ - 按~键关闭喵~ 挚爱小龙制作-请勿乱传制作不易",20,16777215,true);
         titleText.x = 20;
         titleText.y = 15;
         titleText.width = 800;
         addChild(titleText);
         closeBtn = createAnimeButton("❌",16739229,40,40);
         closeBtn.x = 850;
         closeBtn.y = 5;
         addChild(closeBtn);
         createCategoryTabs();
         buttonContainer = new Sprite();
         buttonContainer.x = 15;
         buttonContainer.y = 95;
         addChild(buttonContainer);
         inputContainer = new Sprite();
         inputContainer.x = 15;
         inputContainer.y = 405;
         addChild(inputContainer);
         createInputBox();
         outputText = createTextField("",14,4868682,false);
         outputText.x = 20;
         outputText.y = 460;
         outputText.width = 860;
         outputText.height = 130;
         outputText.border = true;
         outputText.borderColor = 16738740;
         outputText.background = true;
         outputText.backgroundColor = 16775420;
         outputText.multiline = true;
         outputText.wordWrap = true;
         addChild(outputText);
         showCategory("玩家");
         addOutput("✨ ═══ GM魔法控制台启动成功喵~ ═══ ✨");
         addOutput("🌸 欢迎来到二次元世界的管理后台~");
         addOutput("💖 点击上方可爱的分类标签选择功能喵");
         addOutput("⭐ 点击功能按钮释放GM魔法~");
         addOutput("🎀 部分魔法需要输入参数才能生效哦~");
      }
      
      private function createInputBox() : void
      {
         var cancelBtn:Sprite;
         inputLabel = createTextField("🌸 请输入数值/名称喵~:",14,16716947,true);
         inputLabel.x = 0;
         inputLabel.y = 3;
         inputLabel.width = 200;
         inputLabel.height = 25;
         inputContainer.addChild(inputLabel);
         inputBox = createTextField("",14,4868682,false);
         inputBox.x = 205;
         inputBox.y = 0;
         inputBox.width = 400;
         inputBox.height = 30;
         inputBox.border = true;
         inputBox.borderColor = 16738740;
         inputBox.background = true;
         inputBox.backgroundColor = 16775420;
         inputBox.type = TextFieldType.INPUT;
         inputBox.maxChars = 100;
         inputContainer.addChild(inputBox);
         inputBtn = createAnimeButton("💖 确认",16738740,90,30);
         inputBtn.x = 615;
         inputBtn.y = 0;
         inputBtn.addEventListener(MouseEvent.CLICK,onInputConfirm);
         inputContainer.addChild(inputBtn);
         cancelBtn = createAnimeButton("❌ 取消",16737894,90,30);
         cancelBtn.x = 715;
         cancelBtn.y = 0;
         cancelBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            inputContainer.visible = false;
            addOutput("❌ 已取消输入");
         });
         inputContainer.addChild(cancelBtn);
         inputContainer.visible = false;
      }
      
      private function createCategoryTabs() : void
      {
         var i:int = 0;
         while(i < categoryNames.length)
         {
            var tab:Sprite = createAnimeTabButton(categoryNames[i],getAnimeColor(i),62,42);
            tab.x = 20 + i * 64;
            tab.y = 55;
            tab.name = categoryNames[i];
            addChild(tab);
            categoryBtns.push(tab);
            i++;
         }
         updateTabSelection("玩家");
      }
      
      private function updateTabSelection(selectedCategory:String) : void
      {
         var i:int = 0;
         while(i < categoryBtns.length)
         {
            var tab:Sprite = categoryBtns[i];
            var isSelected:Boolean = tab.name == selectedCategory;
            var g:Graphics = tab.graphics;
            g.clear();
            if(isSelected)
            {
               g.beginFill(categoryColors[i],1);
               g.lineStyle(2,16777215,1);
            }
            else
            {
               g.beginFill(categoryColors[i],0.7);
               g.lineStyle(1,8947848,1);
            }
            g.drawRoundRect(0,0,65,35,8,8);
            g.endFill();
            i++;
         }
      }
      
      private function createTextField(text:String, size:int, color:uint, bold:Boolean) : TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Microsoft YaHei";
         format.size = size;
         format.color = color;
         format.bold = bold;
         format.leading = 3;
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.selectable = false;
         tf.antiAliasType = "advanced";
         tf.gridFitType = "pixel";
         return tf;
      }
      
      private function drawCuteDecorations(g:Graphics) : void
      {
         var i:int = 0;
         while(i < 8)
         {
            var x:Number = 50 + i * 100 + Math.random() * 50;
            var y:Number = 50 + Math.random() * 500;
            drawStar(g,x,y,8,16766720,0.6);
            i++;
         }
         var j:int = 0;
         while(j < 6)
         {
            var hx:Number = 100 + j * 120 + Math.random() * 60;
            var hy:Number = 80 + Math.random() * 400;
            drawHeart(g,hx,hy,6,16738740,0.4);
            j++;
         }
      }
      
      private function drawStarDecorations(g:Graphics) : void
      {
         var i:int = 0;
         while(i < 12)
         {
            var x:Number = 60 + i * 70;
            var y:Number = 10 + Math.random() * 20;
            drawStar(g,x,y,5,16777215,0.8);
            i++;
         }
      }
      
      private function drawStar(g:Graphics, x:Number, y:Number, size:Number, color:uint, alpha:Number) : void
      {
         g.beginFill(color,alpha);
         g.moveTo(x,y - size);
         g.lineTo(x + size * 0.3,y - size * 0.3);
         g.lineTo(x + size,y);
         g.lineTo(x + size * 0.3,y + size * 0.3);
         g.lineTo(x,y + size);
         g.lineTo(x - size * 0.3,y + size * 0.3);
         g.lineTo(x - size,y);
         g.lineTo(x - size * 0.3,y - size * 0.3);
         g.lineTo(x,y - size);
         g.endFill();
      }
      
      private function drawHeart(g:Graphics, x:Number, y:Number, size:Number, color:uint, alpha:Number) : void
      {
         g.beginFill(color,alpha);
         g.moveTo(x,y + size);
         g.curveTo(x - size,y - size / 2,x - size / 2,y - size / 2);
         g.curveTo(x,y - size,x + size / 2,y - size / 2);
         g.curveTo(x + size,y - size / 2,x,y + size);
         g.endFill();
      }
      
      private function getAnimeColor(index:int) : uint
      {
         var animeColors:Array = [16738740,8900331,10025880,16766720,14524637,16737095,4251856,15787660,16716947,9662683,52945,16753920,14315734,3329330];
         return animeColors[index % animeColors.length];
      }
      
      private function createButton(text:String, color:uint, width:int = 70, height:int = 25) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.8);
         g.drawRoundRect(0,0,width,height,5,5);
         g.endFill();
         g.lineStyle(1,16777215,1);
         g.drawRoundRect(0,0,width,height,5,5);
         var tf:TextField = createTextField(text,12,16777215,true);
         tf.x = 5;
         tf.y = (height - 15) / 2;
         tf.width = width - 10;
         tf.height = 15;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createTabButton(text:String, color:uint, width:int, height:int) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.7);
         g.drawRoundRect(0,0,width,height,8,8);
         g.endFill();
         g.lineStyle(1,8947848,1);
         g.drawRoundRect(0,0,width,height,8,8);
         var tf:TextField = createTextField(text,12,16777215,true);
         tf.x = 5;
         tf.y = (height - 16) / 2;
         tf.width = width - 10;
         tf.height = 18;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createModernTabButton(text:String, color:uint, width:int, height:int) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.85);
         g.drawRoundRect(0,0,width,height,10,10);
         g.endFill();
         g.lineStyle(2,16777215,0.3);
         g.drawRoundRect(1,1,width - 2,height - 2,9,9);
         g.lineStyle(1,0,0.2);
         g.drawRoundRect(0,0,width,height,10,10);
         var tf:TextField = createTextField(text,12,16777215,true);
         tf.x = 3;
         tf.y = (height - 16) / 2;
         tf.width = width - 6;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createAnimeButton(text:String, color:uint, width:int, height:int) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.9);
         g.drawRoundRect(0,0,width,height,15,15);
         g.endFill();
         g.beginFill(16777215,0.4);
         g.drawRoundRect(3,3,width - 6,height / 2 - 3,12,12);
         g.endFill();
         g.lineStyle(2,16777215,0.8);
         g.drawRoundRect(0,0,width,height,15,15);
         g.lineStyle(1,16766720,0.6);
         g.drawRoundRect(2,2,width - 4,height - 4,13,13);
         var tf:TextField = createTextField(text,12,16777215,true);
         tf.x = 5;
         tf.y = (height - 16) / 2;
         tf.width = width - 10;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createAnimeTabButton(text:String, color:uint, width:int, height:int) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.85);
         g.drawRoundRect(0,0,width,height,12,12);
         g.endFill();
         g.beginFill(16777215,0.3);
         g.drawRoundRect(2,2,width - 4,height / 2 - 2,10,10);
         g.endFill();
         g.lineStyle(2,16777215,0.7);
         g.drawRoundRect(0,0,width,height,12,12);
         drawStar(g,width - 10,8,3,16777215,0.8);
         var tf:TextField = createTextField(text,11,16777215,true);
         tf.x = 3;
         tf.y = (height - 16) / 2;
         tf.width = width - 6;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         return btn;
      }
      
      private function createFunctionButton(text:String, description:String, color:uint = 16738740) : Sprite
      {
         var btn:Sprite = new Sprite();
         var g:Graphics = btn.graphics;
         g.beginFill(color,0.9);
         g.drawRoundRect(0,0,210,40,15,15);
         g.endFill();
         g.beginFill(16777215,0.3);
         g.drawRoundRect(3,3,204,18,12,12);
         g.endFill();
         g.lineStyle(2,16777215,0.8);
         g.drawRoundRect(0,0,210,40,15,15);
         g.lineStyle(1,16766720,0.6);
         g.drawRoundRect(2,2,206,36,13,13);
         drawHeart(g,195,10,4,16777215,0.7);
         var tf:TextField = createTextField(text,12,16777215,true);
         tf.x = 10;
         tf.y = 12;
         tf.width = 185;
         tf.height = 20;
         btn.addChild(tf);
         btn.buttonMode = true;
         btn.mouseChildren = false;
         btn.name = description;
         btn.addEventListener(MouseEvent.MOUSE_OVER,onAnimeFunctionButtonOver);
         btn.addEventListener(MouseEvent.MOUSE_OUT,onAnimeFunctionButtonOut);
         return btn;
      }
      
      private function setupEventListeners() : void
      {
         closeBtn.addEventListener(MouseEvent.CLICK,hide);
         closeBtn.addEventListener(MouseEvent.MOUSE_OVER,onCloseButtonOver);
         closeBtn.addEventListener(MouseEvent.MOUSE_OUT,onCloseButtonOut);
         var i:int = 0;
         while(i < categoryBtns.length)
         {
            categoryBtns[i].addEventListener(MouseEvent.CLICK,onCategoryClick);
            categoryBtns[i].addEventListener(MouseEvent.MOUSE_OVER,onTabMouseOver);
            categoryBtns[i].addEventListener(MouseEvent.MOUSE_OUT,onTabMouseOut);
            i++;
         }
      }
      
      private function onCategoryClick(e:MouseEvent) : void
      {
         var categoryName:String = e.currentTarget.name;
         showCategory(categoryName);
      }
      
      private function onCloseButtonOver(e:MouseEvent) : void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleY = 1.15;
         btn.scaleX = 1.15;
         btn.alpha = 0.9;
         btn.rotation = 15;
      }
      
      private function onCloseButtonOut(e:MouseEvent) : void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleY = 1;
         btn.scaleX = 1;
         btn.alpha = 1;
         btn.rotation = 0;
      }
      
      private function onTabMouseOver(e:MouseEvent) : void
      {
         var tab:Sprite = e.currentTarget as Sprite;
         if(tab.name != currentCategory)
         {
            tab.alpha = 0.9;
            tab.scaleY = 1.08;
            tab.scaleX = 1.08;
            tab.y -= 2;
         }
      }
      
      private function onTabMouseOut(e:MouseEvent) : void
      {
         var tab:Sprite = e.currentTarget as Sprite;
         if(tab.name != currentCategory)
         {
            tab.alpha = 1;
            tab.scaleY = 1;
            tab.scaleX = 1;
            tab.y += 2;
         }
      }
      
      private function onAnimeFunctionButtonOver(e:MouseEvent) : void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleY = 1.08;
         btn.scaleX = 1.08;
         btn.alpha = 0.95;
         var g:Graphics = btn.graphics;
         g.clear();
         g.beginFill(16766720,0.3);
         g.drawRoundRect(-3,-3,216,46,18,18);
         g.endFill();
         g.beginFill(16738740,1);
         g.drawRoundRect(0,0,210,40,15,15);
         g.endFill();
         g.beginFill(16777215,0.5);
         g.drawRoundRect(3,3,204,18,12,12);
         g.endFill();
         g.lineStyle(3,16766720,1);
         g.drawRoundRect(0,0,210,40,15,15);
         g.lineStyle(1,16777215,0.8);
         g.drawRoundRect(2,2,206,36,13,13);
         var i:int = 0;
         while(i < 3)
         {
            var sx:Number = 20 + i * 60 + Math.random() * 40;
            var sy:Number = 8 + Math.random() * 24;
            drawStar(g,sx,sy,3,16777215,0.9);
            i++;
         }
      }
      
      private function onAnimeFunctionButtonOut(e:MouseEvent) : void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         btn.scaleY = 1;
         btn.scaleX = 1;
         btn.alpha = 1;
         var g:Graphics = btn.graphics;
         g.clear();
         g.beginFill(16738740,0.9);
         g.drawRoundRect(0,0,210,40,15,15);
         g.endFill();
         g.beginFill(16777215,0.3);
         g.drawRoundRect(3,3,204,18,12,12);
         g.endFill();
         g.lineStyle(2,16777215,0.8);
         g.drawRoundRect(0,0,210,40,15,15);
         g.lineStyle(1,16766720,0.6);
         g.drawRoundRect(2,2,206,36,13,13);
         drawHeart(g,195,10,4,16777215,0.7);
      }
      
      private function showCategory(categoryName:String) : void
      {
         currentCategory = categoryName;
         updateTabSelection(categoryName);
         createFunctionButtons(categoryName);
      }
      
      private function createFunctionButtons(category:String) : void
      {
         while(buttonContainer.numChildren > 0)
         {
            buttonContainer.removeChildAt(0);
         }
         functionBtns = [];
         var buttons:Array = getFunctionButtons(category);
         addOutput("正在创建 " + category + " 分类的 " + buttons.length + " 个按钮");
         var i:int = 0;
         while(i < buttons.length)
         {
            var btnData:Object = buttons[i];
            var btn:Sprite = createFunctionButton(btnData.text,btnData.command,uint(btnData.color) || true);
            var row:int = Math.floor(i / 4);
            var col:int = i % 4;
            btn.x = col * 220;
            btn.y = row * 48;
            btn.addEventListener(MouseEvent.CLICK,onFunctionClick);
            buttonContainer.addChild(btn);
            functionBtns.push(btn);
            i++;
         }
         addOutput("按钮创建完成，共 " + functionBtns.length + " 个");
      }
      
      private function getFunctionButtons(category:String) : Array
      {
         switch(category)
         {
            case "玩家":
               return [{
                  "text":"等级+1",
                  "command":"player_addlevel_1",
                  "color":26112
               },{
                  "text":"等级+10",
                  "command":"player_addlevel_10",
                  "color":26112
               },{
                  "text":"等级100",
                  "command":"player_level_100",
                  "color":34816
               },{
                  "text":"满级999",
                  "command":"player_level_999",
                  "color":43520
               },{
                  "text":"自定义等级",
                  "command":"player_level_custom",
                  "color":26367
               },{
                  "text":"经验+1000",
                  "command":"player_exp_1000",
                  "color":26112
               },{
                  "text":"经验+10000",
                  "command":"player_exp_10000",
                  "color":34816
               },{
                  "text":"自定义经验",
                  "command":"player_exp_custom",
                  "color":26367
               },{
                  "text":"银币+1000",
                  "command":"player_coin_1000",
                  "color":26112
               },{
                  "text":"银币+99999",
                  "command":"player_coin_99999",
                  "color":34816
               },{
                  "text":"自定义银币",
                  "command":"player_coin_custom",
                  "color":26367
               },{
                  "text":"积分+1000",
                  "command":"player_score_1000",
                  "color":26112
               },{
                  "text":"积分+50000",
                  "command":"player_score_50000",
                  "color":34816
               },{
                  "text":"自定义积分",
                  "command":"player_score_custom",
                  "color":26367
               },{
                  "text":"解除作弊",
                  "command":"player_nocheat",
                  "color":16737792
               },{
                  "text":"开启新队友",
                  "command":"player_newpartner",
                  "color":26367
               },{
                  "text":"巅峰等级+10",
                  "command":"player_addpeaklv_10",
                  "color":26112
               },{
                  "text":"巅峰经验+1000",
                  "command":"player_peakexp_1000",
                  "color":34816
               },{
                  "text":"设置称号",
                  "command":"player_title",
                  "color":43520
               },{
                  "text":"获取全部称号",
                  "command":"player_allheads",
                  "color":52224
               },{
                  "text":"修改昵称",
                  "command":"player_nickname",
                  "color":26367,
                  "needInput":true
               }];
            case "装备":
               return [{
                  "text":"自定义添加装备",
                  "command":"equip_custom",
                  "color":16737792,
                  "needInput":true
               },{
                  "text":"添加最强套装",
                  "command":"equip_bestsuits",
                  "color":6684927
               },{
                  "text":"添加所有黑色武器",
                  "command":"equip_blackarms",
                  "color":16711782
               },{
                  "text":"装备碎片+999",
                  "command":"equip_chip_999",
                  "color":43520
               },{
                  "text":"武器碎片+999",
                  "command":"equip_armschip_999",
                  "color":34816
               },{
                  "text":"添加所有时装",
                  "command":"equip_fashion",
                  "color":26367
               },{
                  "text":"添加所有装置",
                  "command":"equip_device_10",
                  "color":26112
               },{
                  "text":"添加所有兵器",
                  "command":"equip_weapon_10",
                  "color":34816
               },{
                  "text":"武器升级到99",
                  "command":"equip_upgradearms",
                  "color":43520
               },{
                  "text":"清空装备背包",
                  "command":"equip_clear",
                  "color":16737792
               },{
                  "text":"清空武器背包",
                  "command":"arms_clear",
                  "color":16724736
               },{
                  "text":"扩展装备格子",
                  "command":"equip_expandbag",
                  "color":10053375
               },{
                  "text":"扩展武器格子",
                  "command":"arms_expandbag",
                  "color":6724095
               }];
            case "关卡":
               return [{
                  "text":"🗼 一键通关虚天塔",
                  "command":"tower_complete_all",
                  "color":8900331
               },{
                  "text":"胜利当前关卡",
                  "command":"level_win",
                  "color":65382
               },{
                  "text":"重启当前关卡",
                  "command":"level_restart",
                  "color":16737792
               },{
                  "text":"解锁所有地图",
                  "command":"map_unlockall",
                  "color":43520
               },{
                  "text":"通关所有地图",
                  "command":"map_winall",
                  "color":26367
               },{
                  "text":"解锁所有秘境",
                  "command":"map_unlockwilder",
                  "color":26112
               },{
                  "text":"秘境钥匙=10",
                  "command":"map_wilderkey_10",
                  "color":34816
               },{
                  "text":"扫荡次数=10",
                  "command":"map_sweep_10",
                  "color":43520
               },{
                  "text":"扫荡次数=99",
                  "command":"map_sweep_99",
                  "color":26367
               },{
                  "text":"Boss血量50%",
                  "command":"level_bosslife_50",
                  "color":16737792
               },{
                  "text":"Boss血量1%",
                  "command":"level_bosslife_1",
                  "color":16711782
               },{
                  "text":"杀死Boss",
                  "command":"level_killboss",
                  "color":16711680
               },{
                  "text":"测试敌人开关",
                  "command":"level_testenemy",
                  "color":26112
               }];
            case "物品":
               return [{
                  "text":"自定义添加物品",
                  "command":"item_custom",
                  "color":16737792,
                  "needInput":true
               },{
                  "text":"所有食材+99",
                  "command":"food_raw_99",
                  "color":43520
               },{
                  "text":"清空物品背包",
                  "command":"things_clear",
                  "color":16737792
               },{
                  "text":"清空基因背包",
                  "command":"gene_clear",
                  "color":16724736
               },{
                  "text":"清空零件背包",
                  "command":"parts_clear",
                  "color":16711782
               },{
                  "text":"清空技能背包",
                  "command":"skill_clear",
                  "color":16711731
               },{
                  "text":"武器背包=200",
                  "command":"bag_arms_200",
                  "color":26112
               },{
                  "text":"装备背包=200",
                  "command":"bag_equip_200",
                  "color":34816
               },{
                  "text":"物品背包=200",
                  "command":"bag_things_200",
                  "color":43520
               },{
                  "text":"基因背包=200",
                  "command":"bag_gene_200",
                  "color":26367
               },{
                  "text":"零件背包=200",
                  "command":"bag_parts_200",
                  "color":26112
               }];
            case "系统":
               return [{
                  "text":"帧数30",
                  "command":"system_fps_30",
                  "color":26112
               },{
                  "text":"帧数60",
                  "command":"system_fps_60",
                  "color":34816
               },{
                  "text":"帧数120",
                  "command":"system_fps_120",
                  "color":43520
               },{
                  "text":"新的一天",
                  "command":"system_newday",
                  "color":26367
               },{
                  "text":"本地时间",
                  "command":"system_localtime",
                  "color":26112
               },{
                  "text":"缩放50%",
                  "command":"ui_scale_50",
                  "color":16737792
               },{
                  "text":"恢复缩放",
                  "command":"ui_scale_100",
                  "color":43520
               }];
            case "存档":
               return [{
                  "text":"初始化存档",
                  "command":"save_init",
                  "color":16750848
               },{
                  "text":"复制Json存档",
                  "command":"save_copyjson",
                  "color":26112
               },{
                  "text":"复制Xml存档",
                  "command":"save_copyxml",
                  "color":34816
               },{
                  "text":"修复存档数据",
                  "command":"save_repair",
                  "color":43520
               },{
                  "text":"查询异常原因",
                  "command":"save_checkerror",
                  "color":26367
               },{
                  "text":"自动存档30s",
                  "command":"save_auto_30",
                  "color":26112
               },{
                  "text":"自动存档60s",
                  "command":"save_auto_60",
                  "color":34816
               },{
                  "text":"自动存档300s",
                  "command":"save_auto_300",
                  "color":43520
               }];
            case "任务":
               return [{
                  "text":"解锁主线任务",
                  "command":"task_unlockmain",
                  "color":10027263
               },{
                  "text":"🎯 完成全部任务",
                  "command":"task_completeall",
                  "color":16716947
               },{
                  "text":"解锁任务系统",
                  "command":"task_unlocksystem",
                  "color":26367
               },{
                  "text":"重置任务数据",
                  "command":"task_reset",
                  "color":16737792
               },{
                  "text":"设置秘境次数0",
                  "command":"wilder_setnum_0",
                  "color":34816
               }];
            case "技能":
               return [{
                  "text":"删除当前技能",
                  "command":"skill_clear",
                  "color":16737792
               }];
            case "宠物":
               return [{
                  "text":"🔍 调试宠物系统",
                  "command":"pet_debug",
                  "color":16724838
               },{
                  "text":"🧬 获取所有基因体",
                  "command":"pet_getall",
                  "color":16711833
               },{
                  "text":"🧬 安全添加基因体",
                  "command":"pet_getsafe",
                  "color":65433
               },{
                  "text":"🧬 简化添加基因体",
                  "command":"pet_getfew",
                  "color":6750105
               },{
                  "text":"📦 扩展宠物格子",
                  "command":"pet_expandbag",
                  "color":16737792
               },{
                  "text":"🎒 扩展尸宠背包+10",
                  "command":"pet_addbag_10",
                  "color":26112
               },{
                  "text":"🎒 扩展尸宠背包+50",
                  "command":"pet_addbag_50",
                  "color":34816
               },{
                  "text":"🎒 自定义扩展尸宠背包",
                  "command":"pet_addbag_custom",
                  "color":16750848,
                  "needInput":true
               },{
                  "text":"🚀 自定义飞船经验",
                  "command":"pet_craftexp_custom",
                  "color":34816,
                  "needInput":true
               },{
                  "text":"🚀 自定义飞船等级",
                  "command":"pet_craftlv_custom",
                  "color":26367,
                  "needInput":true
               },{
                  "text":"📈 自定义宠物等级",
                  "command":"pet_setlv_custom",
                  "color":34816,
                  "needInput":true
               },{
                  "text":"🧪 测试单个基因体",
                  "command":"pet_testsingle",
                  "color":16724838
               }];
            case "竞技":
               return [{
                  "text":"竞技场次数+10",
                  "command":"arena_num_10",
                  "color":16737945
               },{
                  "text":"竞技场次数+99",
                  "command":"arena_num_99",
                  "color":26112
               },{
                  "text":"竞技场分数+1000",
                  "command":"arena_score_1000",
                  "color":34816
               },{
                  "text":"竞技场分数+9999",
                  "command":"arena_score_9999",
                  "color":43520
               }];
            case "军团":
               return [{
                  "text":"军衔等级1",
                  "command":"union_rank_1",
                  "color":6750105
               },{
                  "text":"军衔等级5",
                  "command":"union_rank_5",
                  "color":26112
               },{
                  "text":"军衔等级10",
                  "command":"union_rank_10",
                  "color":34816
               },{
                  "text":"军衔等级20",
                  "command":"union_rank_20",
                  "color":43520
               },{
                  "text":"军队等级+1",
                  "command":"union_army_1",
                  "color":26112
               },{
                  "text":"军队等级+5",
                  "command":"union_army_5",
                  "color":34816
               },{
                  "text":"军队等级+10",
                  "command":"union_army_10",
                  "color":43520
               },{
                  "text":"设置贡献值",
                  "command":"union_contribution",
                  "color":26367
               }];
            case "成就":
               return [{
                  "text":"🏆 解锁全部成就",
                  "command":"achieve_unlockall",
                  "color":16739229
               },{
                  "text":"🔄 强制完成所有成就",
                  "command":"achieve_forceall",
                  "color":16716947
               },{
                  "text":"杀敌数+1000",
                  "command":"achieve_kill_1000",
                  "color":26112
               },{
                  "text":"杀敌数+10000",
                  "command":"achieve_kill_10000",
                  "color":34816
               },{
                  "text":"不掉橙装+10",
                  "command":"achieve_noorange_10",
                  "color":43520
               },{
                  "text":"擒王等级+10",
                  "command":"achieve_king_10",
                  "color":26367
               },{
                  "text":"全是银币+10",
                  "command":"achieve_coin_10",
                  "color":26112
               },{
                  "text":"全是橙装+5",
                  "command":"achieve_orange_5",
                  "color":34816
               }];
            case "时间":
               return [{
                  "text":"自定义双倍材料时间",
                  "command":"time_doublematerial_custom",
                  "color":26112,
                  "needInput":true
               },{
                  "text":"自定义双倍经验时间",
                  "command":"time_doubleexp_custom",
                  "color":34816,
                  "needInput":true
               },{
                  "text":"自定义双倍武器时间",
                  "command":"time_doublearms_custom",
                  "color":43520,
                  "needInput":true
               },{
                  "text":"自定义双倍装备时间",
                  "command":"time_doubleequip_custom",
                  "color":26367,
                  "needInput":true
               },{
                  "text":"清除所有双倍时间",
                  "command":"time_clearall",
                  "color":16737792
               }];
            case "自定义":
               return [{
                  "text":"🎴 自定义魂卡属性",
                  "command":"custom_card",
                  "color":16716947
               },{
                  "text":"⚔️ 自定义装备属性",
                  "command":"custom_equip",
                  "color":16739229
               },{
                  "text":"🔫 自定义武器属性",
                  "command":"custom_weapon",
                  "color":16729344
               },{
                  "text":"🚗 自定义载具属性",
                  "command":"custom_vehicle",
                  "color":3329330
               },{
                  "text":"✨ 自定义技能属性",
                  "command":"custom_skill",
                  "color":9662683
               },{
                  "text":"🎁 获取自定义魂卡",
                  "command":"custom_getcard",
                  "color":16738740
               },{
                  "text":"📦 获取自定义装备",
                  "command":"custom_getequip",
                  "color":8900331
               },{
                  "text":"🎯 获取自定义武器",
                  "command":"custom_getweapon",
                  "color":16753920
               },{
                  "text":"🚀 获取自定义载具",
                  "command":"custom_getvehicle",
                  "color":10025880
               },{
                  "text":"⭐ 获取自定义技能",
                  "command":"custom_getskill",
                  "color":14524637
               }];
            case "其他":
               return [{
                  "text":"🎯自定义添加系统",
                  "command":"custom_addsystem",
                  "color":16711782
               },{
                  "text":"添加妞",
                  "command":"more_addgirl",
                  "color":10066431
               },{
                  "text":"设置黄金1000",
                  "command":"pay_gold_1000",
                  "color":26112
               },{
                  "text":"设置黄金9999",
                  "command":"pay_gold_9999",
                  "color":34816
               },{
                  "text":"充值100",
                  "command":"pay_recharge_100",
                  "color":43520
               },{
                  "text":"充值1000",
                  "command":"pay_recharge_1000",
                  "color":26367
               },{
                  "text":"显示充值统计",
                  "command":"pay_showcount",
                  "color":16737792
               },{
                  "text":"时间加速2倍",
                  "command":"time_speed_2",
                  "color":26112
               },{
                  "text":"导出所有ID",
                  "command":"export_allids",
                  "color":16750848
               },{
                  "text":"自定义职务等级",
                  "command":"other_postlv_custom",
                  "color":26367,
                  "needInput":true
               },{
                  "text":"自定义职务经验",
                  "command":"other_postexp_custom",
                  "color":26112,
                  "needInput":true
               },{
                  "text":"自定义粽子数量",
                  "command":"other_zongzi_custom",
                  "color":34816,
                  "needInput":true
               },{
                  "text":"自定义总厨艺值",
                  "command":"other_cookskill_custom",
                  "color":43520,
                  "needInput":true
               },{
                  "text":"清除尸宠背包",
                  "command":"other_clearpets",
                  "color":16737792
               },{
                  "text":"设置存档异常",
                  "command":"other_setcheat",
                  "color":16724736
               },{
                  "text":"清除溢出物品",
                  "command":"other_clearoverflow",
                  "color":16750848
               }];
            default:
               return [];
         }
      }
      
      private function onFunctionClick(e:MouseEvent) : void
      {
         var btn:Sprite = e.currentTarget as Sprite;
         var command:String = btn.name;
         if(needsInput(command))
         {
            showInputBox(command);
         }
         else
         {
            executeGMFunction(command);
         }
      }
      
      private function needsInput(command:String) : Boolean
      {
         var inputCommands:Array = ["player_title","player_nickname","player_level_custom","player_exp_custom","player_coin_custom","player_score_custom","equip_custom","item_custom","pet_craftexp_custom","pet_craftlv_custom","pet_setlv_custom","pet_addbag_custom","other_postlv_custom","other_postexp_custom","other_zongzi_custom","other_cookskill_custom","time_doublematerial_custom","time_doubleexp_custom","time_doublearms_custom","time_doubleequip_custom"];
         return inputCommands.indexOf(command) != -1;
      }
      
      private function showInputBox(command:String) : void
      {
         currentInputCommand = command;
         inputContainer.visible = true;
         inputBox.text = "";
         var hints:Object = {
            "player_title":"请输入称号名称:",
            "player_nickname":"请输入新昵称:",
            "player_level_custom":"请输入等级数值:",
            "player_exp_custom":"请输入经验数值:",
            "player_coin_custom":"请输入银币数值:",
            "player_score_custom":"请输入积分数值:",
            "equip_custom":"请输入装备名称:",
            "item_custom":"请输入物品名称:",
            "pet_craftexp_custom":"请输入飞船经验数值:",
            "pet_craftlv_custom":"请输入飞船等级数值:",
            "pet_setlv_custom":"请输入宠物等级数值:",
            "pet_addbag_custom":"请输入要扩展的尸宠背包格子数量:",
            "other_postlv_custom":"请输入职务等级数值:",
            "other_postexp_custom":"请输入职务经验数值:",
            "other_zongzi_custom":"请输入粽子数量:",
            "other_cookskill_custom":"请输入总厨艺值:",
            "time_doublematerial_custom":"请输入双倍材料时间(分钟):",
            "time_doubleexp_custom":"请输入双倍经验时间(分钟):",
            "time_doublearms_custom":"请输入双倍武器时间(分钟):",
            "time_doubleequip_custom":"请输入双倍装备时间(分钟):"
         };
         inputLabel.text = "请输入数值:";
         addOutput("请在输入框中输入相应的值，然后点击确认");
      }
      
      private function onInputConfirm(e:MouseEvent = null) : void
      {
         var inputValue:String = inputBox.text.replace(/^\s+|\s+$/g,"");
         if(inputValue == "")
         {
            addOutput("❌ 输入不能为空");
            return;
         }
         inputContainer.visible = false;
         executeGMFunctionWithInput(currentInputCommand,inputValue);
      }
      
      private function executeGMFunctionWithInput(command:String, inputValue:String) : void
      {
         var result:String;
         var numValue:int;
         var titleMap:Object;
         var headCode:String;
         var headDefine:*;
         var equipDefine:*;
         var equipSave:*;
         var thingsDefine:*;
         var thingsDefine2:*;
         var addResult:Boolean;
         var addResult2:Boolean;
         var boss:*;
         var militaryDef:*;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            result = "";
            numValue = int(parseInt(inputValue));
            switch(command)
            {
               case "player_title":
                  try
                  {
                     titleMap = {
                        "十年相伴":"anniver10",
                        "九周年快乐":"anniver9",
                        "八周年快乐":"anniver8",
                        "成就之皇":"achieveKing",
                        "成就之神":"achieveGod",
                        "武器制造大师":"armsSkinCreator",
                        "群组达人":"bbs23",
                        "十二生肖":"zodiac12",
                        "霞光领主":"battle4",
                        "霞光天军":"battle3",
                        "霞光雄狮":"battle2",
                        "霞光劲旅":"battle1",
                        "愚人欢乐365":"joyousFool",
                        "佣兵之王":"gameKing",
                        "佣兵精英":"gameSuper"
                     };
                     headCode = titleMap[inputValue];
                     if(headCode)
                     {
                        Gaming.PG.da.head.addHead(headCode,Gaming.api.save.getNowServerDate().getStr());
                        result = "添加称号: " + inputValue;
                     }
                     else
                     {
                        headDefine = Gaming.defineGroup.head.getCnDefine(inputValue);
                        if(headDefine)
                        {
                           Gaming.PG.da.head.addHead(headDefine.name,Gaming.api.save.getNowServerDate().getStr());
                           result = "添加称号: " + inputValue;
                        }
                        else
                        {
                           Gaming.PG.da.head.addHead(inputValue,Gaming.api.save.getNowServerDate().getStr());
                           result = "添加称号代码: " + inputValue;
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result = "称号添加失败: " + inputValue + " (可能不存在此称号)\n常见称号: 十年相伴, 成就之皇, 佣兵之王, 霞光领主等";
                     break;
                  }
                  break;
               case "equip_addbyname":
                  try
                  {
                     equipDefine = Gaming.defineGroup.getAllEquipDefine(inputValue);
                     if(!equipDefine)
                     {
                        equipDefine = Gaming.defineGroup.equip.getCnDefine(inputValue);
                     }
                     if(equipDefine)
                     {
                        equipSave = GiftAddit.getEquipSaveByName(equipDefine.name,Gaming.PG.da.level,1);
                        if(equipSave)
                        {
                           Gaming.PG.da.equipBag.addSave(equipSave);
                           result = "添加装备: " + inputValue + " (" + equipDefine.name + ")";
                        }
                        else
                        {
                           result = "装备创建失败: " + inputValue;
                        }
                     }
                     else
                     {
                        result = "装备不存在: " + inputValue;
                     }
                  }
                  catch(e:Error)
                  {
                     result = "装备添加失败: " + e.message;
                     break;
                  }
                  break;
               case "things_addbyname":
                  try
                  {
                     thingsDefine = Gaming.defineGroup.things.getCnDefine(inputValue);
                     if(thingsDefine)
                     {
                        Gaming.PG.da.thingsBag.addDataByName(thingsDefine.name,1);
                        result = "添加物品: " + inputValue + " (" + thingsDefine.name + ")";
                     }
                     else
                     {
                        thingsDefine2 = Gaming.defineGroup.things.getDefine(inputValue);
                        if(thingsDefine2)
                        {
                           Gaming.PG.da.thingsBag.addDataByName(inputValue,1);
                           result = "添加物品: " + inputValue;
                        }
                        else
                        {
                           result = "物品不存在: " + inputValue;
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result = "物品添加失败: " + e.message;
                     break;
                  }
                  break;
               case "player_nickname":
                  Gaming.PG.save.base.playerName = inputValue;
                  result = "设置昵称为: " + inputValue;
                  break;
               case "player_level_custom":
                  Gaming.testCtrl.cheating.doOrder("player","setHeroLv","",numValue);
                  result = "设置等级为: " + numValue;
                  break;
               case "player_exp_custom":
                  Gaming.testCtrl.cheating.doOrder("player","addHeroExp","",numValue);
                  result = "增加经验: " + numValue;
                  break;
               case "player_coin_custom":
                  Gaming.testCtrl.cheating.doOrder("player","addCoin","",numValue);
                  result = "增加银币: " + numValue;
                  break;
               case "player_score_custom":
                  Gaming.testCtrl.cheating.doOrder("player","addSore","",numValue);
                  result = "增加积分: " + numValue;
                  break;
               case "equip_custom":
                  try
                  {
                     addResult = Boolean(this.smartAddItem(inputValue,1));
                     if(addResult)
                     {
                        result = "成功添加装备: " + inputValue;
                     }
                     else
                     {
                        result = "未找到装备: " + inputValue + "，请检查装备名称";
                     }
                  }
                  catch(e:Error)
                  {
                     result = "添加装备失败: " + e.message;
                     break;
                  }
                  break;
               case "item_custom":
                  try
                  {
                     addResult2 = Boolean(this.smartAddItem(inputValue,1));
                     if(addResult2)
                     {
                        result = "成功添加物品: " + inputValue;
                     }
                     else
                     {
                        result = "未找到物品: " + inputValue + "，请检查物品名称";
                     }
                  }
                  catch(e:Error)
                  {
                     result = "添加物品失败: " + e.message;
                     break;
                  }
                  break;
               case "pet_craftexp_custom":
                  Gaming.testCtrl.cheating.doOrder("pet","addNowCraftExp","",numValue);
                  result = "设置飞船经验: " + numValue;
                  break;
               case "pet_craftlv_custom":
                  Gaming.testCtrl.cheating.doOrder("pet","setNowCraftLv","",numValue);
                  result = "设置飞船等级: " + numValue;
                  break;
               case "pet_setlv_custom":
                  Gaming.testCtrl.cheating.doOrder("pet","setPetLv","",numValue);
                  result = "设置宠物等级: " + numValue;
                  break;
               case "pet_addbag_custom":
                  try
                  {
                     Gaming.testCtrl.enabled = true;
                     Gaming.testCtrl.cheating.enabled = true;
                     Gaming.PG.da.pet.addBagNum(numValue);
                     result = "成功扩展尸宠背包 " + numValue + " 个格子！当前背包容量: " + Gaming.PG.da.pet.saveGroup.lockLen;
                  }
                  catch(e:Error)
                  {
                     result = "扩展尸宠背包失败: " + e.message;
                  }
                  break;
               case "other_postlv_custom":
                  try
                  {
                     Gaming.PG.da.post.save.postLv = numValue;
                     result = "设置职务等级为: " + numValue + " 级";
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置职务等级失败: " + e.message;
                     break;
                  }
                  break;
               case "other_postexp_custom":
                  try
                  {
                     Gaming.PG.da.post.save.postExp = numValue;
                     result = "设置职务经验为: " + numValue;
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置职务经验失败: " + e.message;
                     break;
                  }
                  break;
               case "other_zongzi_custom":
                  try
                  {
                     Gaming.PG.da.main.save.zongzi25 = numValue;
                     Gaming.uiGroup.mainUI.show();
                     result = "设置粽子数量为: " + numValue;
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置粽子数量失败: " + e.message;
                     break;
                  }
                  break;
               case "other_cookskill_custom":
                  try
                  {
                     Gaming.PG.da.food.save.profiAll = numValue;
                     Gaming.PG.da.food.addProfi(0);
                     result = "设置总厨艺值为: " + numValue;
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置总厨艺值失败: " + e.message;
                     break;
                  }
                  break;
               case "time_doublematerial_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleMaterialsDropTime = numValue;
                     result = "设置双倍材料时间为: " + numValue + " 分钟";
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍材料时间失败: " + e.message;
                     break;
                  }
                  break;
               case "time_doubleexp_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleExpTime = numValue;
                     result = "设置双倍经验时间为: " + numValue + " 分钟";
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍经验时间失败: " + e.message;
                     break;
                  }
                  break;
               case "time_doublearms_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleArmsDropTime = numValue;
                     result = "设置双倍武器时间为: " + numValue + " 分钟";
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍武器时间失败: " + e.message;
                     break;
                  }
                  break;
               case "time_doubleequip_custom":
                  try
                  {
                     Gaming.PG.SAVE.time.doubleEquipDropTime = numValue;
                     result = "设置双倍装备时间为: " + numValue + " 分钟";
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置双倍装备时间失败: " + e.message;
                     break;
                  }
                  break;
               case "level_bosslife_custom":
                  boss = Gaming.BG.filter.getEnemyBoss();
                  if(boss)
                  {
                     if(numValue <= 0)
                     {
                        Gaming.TG.hurt.toDie(boss);
                        result = "Boss已死亡";
                        break;
                     }
                     boss.getData().setLifePer(numValue / 100);
                     result = "设置Boss血量: " + numValue + "%";
                     break;
                  }
                  result = "当前关卡没有Boss";
                  break;
               case "arena_num_custom":
                  Gaming.PG.save.arena.todayNum = numValue;
                  result = "设置竞技场次数: " + numValue;
                  break;
               case "arena_score_custom":
                  Gaming.PG.save.arena.score = numValue;
                  result = "设置竞技场分数: " + numValue;
                  break;
               case "space_level_custom":
                  try
                  {
                     Gaming.testCtrl.cheating.doOrder("pet","setNowCraftLv","",numValue);
                     result = "设置飞船等级: " + numValue;
                     break;
                  }
                  catch(e:Error)
                  {
                     result = "设置飞船等级失败: " + e.message;
                     break;
                  }
                  break;
               case "union_rank_custom":
                  militaryDef = Gaming.defineGroup.union.military.getDefine(numValue + "");
                  if(militaryDef)
                  {
                     Gaming.testCtrl.cheating.tempContribution = militaryDef.totalMust;
                     result = "设置军衔等级: " + numValue + " (" + militaryDef.cnName + ")";
                     break;
                  }
                  result = "军衔等级 " + numValue + " 不存在";
                  break;
               case "achieve_kill_custom":
                  Gaming.PG.save.count.killNum = numValue;
                  result = "设置杀敌数: " + numValue;
                  break;
               case "time_speed_custom":
                  CountCtrl.onlineTimePer = numValue;
                  result = "设置时间加速倍数: " + numValue;
                  break;
               case "tower_complete_all":
                  executeGMFunction("tower_all");
                  return;
               default:
                  result = "未知的输入命令: " + command;
            }
            addOutput("✅ " + result);
         }
         catch(error:Error)
         {
            addOutput("❌ 执行失败: " + error.message);
         }
      }
      
      private function executeGMFunction(command:String) : void
      {
         var result:String;
         var parts:Array;
         var category:String;
         var action:String;
         var value:String;
         var v:int;
         var currentLevel:int;
         var newLevel:int;
         var currentPeakLevel:int;
         var newPeakLevel:int;
         var successCount:int;
         var currentTime:String;
         var headDefineGroup:*;
         var headDefineArr:Array;
         var headDefine:*;
         var allTitles:Array;
         var titleCode:String;
         var addResult:Boolean;
         var boss:*;
         var boss2:*;
         var totalUnlocked:int;
         var totalCompleted:int;
         var totalAccepted:int;
         var taskTypes:Array;
         var taskType:String;
         var taskObj:Object;
         var taskData:*;
         var taskName:String;
         var addedPets:int;
         var failedPets:int;
         var debugInfo:Array;
         var normalPetNames:Array;
         var petName:String;
         var geneDefine:*;
         var geneSave:*;
         var geneData:*;
         var petData:*;
         var safeAddedPets:int;
         var safeFailedPets:int;
         var safeDebugInfo:Array;
         var safePetNames:Array;
         var safePetName:String;
         var safeGeneDefine:*;
         var fewAddedPets:int;
         var fewFailedPets:int;
         var fewDebugInfo:Array;
         var fewPetNames:Array;
         var fewPetName:String;
         var fewGeneDefine:*;
         var debugResult:Array;
         var normalNames:Array;
         var testPets:Array;
         var testPet:String;
         var testDefine:*;
         var testGeneSave:*;
         var testGeneData:*;
         var testPetName:String;
         var testGeneDefine:*;
         var testPetData:*;
         var craft:*;
         var militaryDef:*;
         var achieveGroup:*;
         var allAchievements:Object;
         var completedCount:int;
         var achieveName:String;
         var achieveData:*;
         var errorCount:int;
         var moreArr:Array;
         var moreBagArr:Array;
         var allMoreArr:Array;
         var loveData:*;
         var setCount:int;
         var moreData:*;
         var moreArr2:Array;
         var moreBagArr2:Array;
         var allMoreArr2:Array;
         var powerCount:int;
         var moreData2:*;
         var towerData:*;
         var towerSave:*;
         var towerDefines:Array;
         var towerDefine:*;
         var towerName:String;
         var maxDiff:int;
         var unendData:*;
         var unendSave:*;
         var maxUnendLevel:int;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            result = "";
            parts = command.split("_");
            category = parts[0];
            action = parts[1];
            value = parts.length > 2 ? parts[2] : "0";
            v = int(parseInt(value));
            loop14:
            switch(category)
            {
               case "player":
                  switch(action)
                  {
                     case "level":
                        Gaming.testCtrl.cheating.doOrder("player","setHeroLv","",v);
                        result = "设置等级为: " + v;
                        break loop14;
                     case "addlevel":
                        currentLevel = Gaming.PG.DATA.base.save.level;
                        newLevel = currentLevel + v;
                        Gaming.testCtrl.cheating.doOrder("player","setHeroLv","",newLevel);
                        result = "等级从 " + currentLevel + " 增加到 " + newLevel;
                        break loop14;
                     case "exp":
                        Gaming.testCtrl.cheating.doOrder("player","addHeroExp","",v);
                        result = "增加经验: " + v;
                        break loop14;
                     case "coin":
                        Gaming.testCtrl.cheating.doOrder("player","addCoin","",v);
                        result = "增加银币: " + v;
                        break loop14;
                     case "score":
                        Gaming.testCtrl.cheating.doOrder("player","addSore","",v);
                        result = "增加积分: " + v;
                        break loop14;
                     case "nocheat":
                        Gaming.testCtrl.cheating.doOrder("player","noZuobi","",0);
                        result = "解除作弊状态";
                        break loop14;
                     case "newpartner":
                        Gaming.testCtrl.cheating.doOrder("player","openNewPartner","",0);
                        result = "开启新队友";
                        break loop14;
                     case "fullhp":
                        Gaming.testCtrl.cheating.doOrder("level","fullHp","",0);
                        result = "满血";
                        break loop14;
                     case "fullmp":
                        Gaming.testCtrl.cheating.doOrder("level","fullMp","",0);
                        result = "满蓝";
                        break loop14;
                     case "godmode":
                        Gaming.testCtrl.cheating.doOrder("level","godMode","",0);
                        result = "切换无敌模式";
                        break loop14;
                     case "reset":
                        Gaming.testCtrl.cheating.doOrder("save","initPlayerSave","",0);
                        result = "重置玩家数据";
                        break loop14;
                     case "peaklv":
                        Gaming.PG.da.peak.setLevel(v);
                        result = "设置巅峰等级: " + v;
                        break loop14;
                     case "addpeaklv":
                        currentPeakLevel = Gaming.PG.da.peak.level;
                        newPeakLevel = currentPeakLevel + v;
                        Gaming.PG.da.peak.setLevel(newPeakLevel);
                        result = "巅峰等级从 " + currentPeakLevel + " 增加到 " + newPeakLevel;
                        break loop14;
                     case "peakexp":
                        Gaming.PG.da.peak.addExp(v);
                        result = "增加巅峰经验: " + v;
                        break loop14;
                     case "title":
                        result = "称号功能需要输入具体称号名";
                        break loop14;
                     case "allheads":
                        try
                        {
                           successCount = 0;
                           currentTime = Gaming.api.save.getNowServerDate().getStr();
                           try
                           {
                              headDefineGroup = Gaming.defineGroup.head;
                              if(headDefineGroup && headDefineGroup.arr)
                              {
                                 headDefineArr = headDefineGroup.arr;
                                 for each(headDefine in headDefineArr)
                                 {
                                    if(headDefine && headDefine.name)
                                    {
                                       try
                                       {
                                          Gaming.PG.da.head.addHead(headDefine.name,currentTime);
                                          successCount++;
                                       }
                                       catch(addError:Error)
                                       {
                                       }
                                    }
                                 }
                              }
                           }
                           catch(systemError:Error)
                           {
                              allTitles = ["anniver10","anniver9","anniver8","anniver7","anniver6","anniver5","achieveKing","achieveGod","achieveSuper","achieveHigh","achieveNormal","armsSkinCreator","bbs23","zodiac12","joyousFool","battle4","battle3","battle2","battle1","gameKing","gameSuper","gameHigh","gameNormal","unionKing","unionSuper","arenaKing","arenaSuper","towerKing","towerSuper","petKing","petSuper","equipKing","equipSuper","levelKing","levelSuper","coinKing","coinSuper","timeKing","timeSuper","killKing","killSuper","bossKing","bossSuper","mapKing","mapSuper","taskKing","taskSuper","loveKing","loveSuper","vipKing","vipSuper","payKing","paySuper","loginKing","loginSuper"];
                              for each(titleCode in allTitles)
                              {
                                 try
                                 {
                                    Gaming.PG.da.head.addHead(titleCode,currentTime);
                                    successCount++;
                                 }
                                 catch(titleError:Error)
                                 {
                                 }
                              }
                           }
                           result = "成功添加 " + successCount + " 个称号！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "获取全部称号失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "nickname":
                        result = "昵称功能需要输入具体昵称";
                  }
                  break;
               case "equip":
                  switch(action)
                  {
                     case "custom":
                        try
                        {
                           addResult = Boolean(this.smartAddItem(inputValue,1));
                           if(addResult)
                           {
                              result = "成功添加装备: " + inputValue;
                           }
                           else
                           {
                              result = "未找到装备: " + inputValue + "，请检查装备名称";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "添加装备失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "bestsuits":
                        try
                        {
                           Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
                           Gaming.PG.da.equipBag.clearData();
                           Gaming.testCtrl.arms.addAllSuit();
                           result = "已清空装备背包并添加所有最强套装！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "添加最强套装失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "blackarms":
                        try
                        {
                           Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
                           Gaming.testCtrl.cheating.doOrder("equip","addBlackArms","",0);
                           result = "添加所有黑色武器成功！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "添加黑色武器失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "chip":
                        Gaming.testCtrl.cheating.doOrder("equip","addEquipChip","",v);
                        result = "添加装备碎片: " + v;
                        break loop14;
                     case "armschip":
                        Gaming.testCtrl.cheating.doOrder("equip","addArmsChip","",v);
                        result = "添加武器碎片: " + v;
                        break loop14;
                     case "fashion":
                        Gaming.testCtrl.cheating.doOrder("equip","addAllFashion","",0);
                        result = "添加所有时装";
                        break loop14;
                     case "device":
                        Gaming.testCtrl.cheating.doOrder("equip","addAllDevice","",v);
                        result = "添加所有装置: " + v;
                        break loop14;
                     case "weapon":
                        Gaming.testCtrl.cheating.doOrder("equip","addAllWeapon","",v);
                        result = "添加所有兵器: " + v;
                        break loop14;
                     case "upgradearms":
                        Gaming.testCtrl.cheating.doOrder("equip","testBlackArms","",0);
                        result = "升级背包所有武器到99级";
                        break loop14;
                     case "clear":
                        Gaming.PG.da.equipBag.clearData();
                        result = "装备背包已清空！";
                        break loop14;
                     case "expandbag":
                        Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
                        result = "装备格子已扩展到200个！";
                  }
                  break;
               case "arms":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.armsBag.clearData();
                        result = "武器背包已清空！";
                        break loop14;
                     case "expandbag":
                        Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
                        result = "武器格子已扩展到200个！";
                  }
                  break;
               case "level":
                  switch(action)
                  {
                     case "win":
                        Gaming.LG.levelWin("r_over");
                        result = "胜利当前关卡";
                        break loop14;
                     case "restart":
                        Gaming.testCtrl.cheating.doOrder("level","restart","",0);
                        result = "重启当前关卡";
                        break loop14;
                     case "testenemy":
                        Gaming.testCtrl.cheating.doOrder("level","testEnemy","",0);
                        result = "切换测试敌人状态";
                        break loop14;
                     case "bosslife":
                        boss = Gaming.BG.filter.getEnemyBoss();
                        if(boss)
                        {
                           if(v <= 0)
                           {
                              Gaming.TG.hurt.toDie(boss);
                              result = "Boss已死亡";
                              break loop14;
                           }
                           boss.getData().setLifePer(v / 100);
                           result = "设置Boss血量: " + v + "%";
                           break loop14;
                        }
                        result = "当前关卡没有Boss";
                        break loop14;
                     case "killboss":
                        boss2 = Gaming.BG.filter.getEnemyBoss();
                        if(boss2)
                        {
                           Gaming.TG.hurt.toDie(boss2);
                           result = "Boss已被杀死";
                           break loop14;
                        }
                        result = "当前关卡没有Boss";
                        break loop14;
                  }
                  break;
               case "map":
                  switch(action)
                  {
                     case "unlockall":
                        Gaming.PG.da.worldMap.saveGroup.unlockAll();
                        Gaming.uiGroup.mainUI.show();
                        result = "解锁所有地图";
                        break loop14;
                     case "winall":
                        Gaming.PG.da.worldMap.saveGroup.unlockAll();
                        Gaming.PG.da.worldMap.saveGroup.winAll();
                        Gaming.uiGroup.mainUI.show();
                        result = "通关所有地图";
                        break loop14;
                     case "unlockwilder":
                        Gaming.PG.da.wilder.unlockAllWider();
                        result = "解锁所有秘境";
                        break loop14;
                     case "wilderkey":
                        Gaming.PG.da.wilder.saveGroup.keyNum = v;
                        result = "设置秘境钥匙: " + v;
                        break loop14;
                     case "sweep":
                        Gaming.PG.da.main.save.daySweeping = v;
                        result = "设置扫荡次数: " + v;
                  }
                  break;
               case "item":
                  switch(action)
                  {
                     case "custom":
                        try
                        {
                           addResult = Boolean(this.smartAddItem(inputValue,1));
                           if(addResult)
                           {
                              result = "成功添加物品: " + inputValue;
                           }
                           else
                           {
                              result = "未找到物品: " + inputValue + "，请检查物品名称";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "添加物品失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "all":
                        Gaming.testCtrl.cheating.doOrder("things","addAllThings","",v);
                        result = "添加所有物品: " + v;
                        break loop14;
                     case "key":
                        Gaming.testCtrl.cheating.doOrder("things","addAllKey","",v);
                        result = "添加钥匙: " + v;
                  }
                  break;
               case "things":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.thingsBag.clearData();
                        result = "物品背包已清空！";
                  }
                  break;
               case "gene":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.geneBag.clearData();
                        result = "基因背包已清空！";
                  }
                  break;
               case "parts":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.partsBag.clearData();
                        result = "零件背包已清空！";
                  }
                  break;
               case "skill":
                  switch(action)
                  {
                     case "clear":
                        Gaming.PG.da.skillBag.clearData();
                        result = "技能背包已清空！";
                  }
                  break;
               case "bag":
                  switch(action)
                  {
                     case "arms":
                        Gaming.PG.da.armsBag.saveGroup.unlockTo(v);
                        result = "武器背包扩展到: " + v + " 个格子";
                        break loop14;
                     case "equip":
                        Gaming.PG.da.equipBag.saveGroup.unlockTo(v);
                        result = "装备背包扩展到: " + v + " 个格子";
                        break loop14;
                     case "things":
                        Gaming.PG.da.thingsBag.saveGroup.unlockTo(v);
                        result = "物品背包扩展到: " + v + " 个格子";
                        break loop14;
                     case "gene":
                        Gaming.PG.da.geneBag.saveGroup.unlockTo(v);
                        result = "基因背包扩展到: " + v + " 个格子";
                        break loop14;
                     case "parts":
                        Gaming.PG.da.partsBag.saveGroup.unlockTo(v);
                        result = "零件背包扩展到: " + v + " 个格子";
                        break loop14;
                     case "skill":
                        Gaming.PG.da.skillBag.saveGroup.unlockTo(v);
                        result = "技能背包扩展到: " + v + " 个格子";
                  }
                  break;
               case "food":
                  switch(action)
                  {
                     case "raw":
                        Gaming.PG.da.food.addRawAll(v);
                        result = "添加所有食材: " + v;
                  }
                  break;
               case "system":
                  switch(action)
                  {
                     case "fps":
                        Gaming.testCtrl.cheating.doOrder("system","setFrame","",v);
                        result = "设置帧数: " + v;
                        break loop14;
                     case "newday":
                        Gaming.testCtrl.cheating.doOrder("system","newDay","",0);
                        result = "触发新的一天";
                        break loop14;
                     case "localtime":
                        Gaming.testCtrl.cheating.doOrder("system","setToLocalTime","",0);
                        result = "切换本地时间";
                  }
                  break;
               case "ui":
                  switch(action)
                  {
                     case "scale":
                        Gaming.testCtrl.cheating.doOrder("ui","scaleScene","",v);
                        result = "设置缩放: " + v + "%";
                  }
                  break;
               case "save":
                  switch(action)
                  {
                     case "init":
                        Gaming.PG.initSave();
                        Gaming.uiGroup.mainUI.show();
                        result = "初始化存档成功";
                        break loop14;
                     case "copyjson":
                        System.setClipboard(JSON2.encode(ClassProperty.copyObj(Gaming.PG.save)));
                        result = "已复制Json存档到剪贴板";
                        break loop14;
                     case "copyxml":
                        System.setClipboard(ObjectToXml.decode4399(ClassProperty.copyObj(Gaming.PG.save)));
                        result = "已复制Xml存档到剪贴板";
                        break loop14;
                     case "repair":
                        PlayerDataSupple.dealSummer(Gaming.PG.da);
                        result = "修复存档数据成功";
                        break loop14;
                     case "checkerror":
                        result = "异常原因: " + Gaming.PG.save.main.zuobiReason;
                        break loop14;
                     case "auto":
                        Gaming.testCtrl.cheating.doOrder("save","autoSaveTime","",v);
                        result = "设置自动存档间隔: " + v + "秒";
                  }
                  break;
               case "task":
                  switch(action)
                  {
                     case "unlockmain":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("task","unlockMainTask","",0);
                           result = "解锁所有主线任务";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "解锁主线任务失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "unlocksystem":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("task","unlockTask","",0);
                           result = "解锁任务系统";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "解锁任务系统失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "completeall":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           totalUnlocked = 0;
                           totalCompleted = 0;
                           totalAccepted = 0;
                           
                           // 确保任务系统存在
                           if(!Gaming.PG || !Gaming.PG.da || !Gaming.PG.da.task)
                           {
                              result = "❌ 任务系统不存在";
                              break loop14;
                           }
                           
                           // 方法1: 先解锁所有类型的任务
                           taskTypes = ["main","day","treasure","extra","king","memory","deputy","spread","week","daily","weekly","monthly"];
                           for each(taskType in taskTypes)
                           {
                              try
                              {
                                 // 使用unlockAllByType方法解锁所有任务
                                 Gaming.PG.da.task.unlockAllByType(taskType);
                                 totalUnlocked++;
                              }
                              catch(unlockError:Error)
                              {
                                 // 忽略单个类型的解锁错误，继续下一个
                              }
                           }
                           
                           // 方法2: 获取所有任务定义并强制添加
                           try
                           {
                              for each(taskType in taskTypes)
                              {
                                 try
                                 {
                                    var taskDefineArr:Array = Gaming.defineGroup.task.getArrByType(taskType);
                                    if(taskDefineArr && taskDefineArr.length > 0)
                                    {
                                       for each(var taskDefine:* in taskDefineArr)
                                       {
                                          if(taskDefine && taskDefine.name)
                                          {
                                             try
                                             {
                                                // 检查任务是否已存在
                                                var existingTask:* = Gaming.PG.da.task.getTaskDataByName(taskDefine.name);
                                                if(!existingTask)
                                                {
                                                   // 添加新任务
                                                   Gaming.PG.da.task.addTask(taskDefine.name);
                                                   totalAccepted++;
                                                }
                                             }
                                             catch(addError:Error)
                                             {
                                                // 忽略添加错误
                                             }
                                          }
                                       }
                                    }
                                 }
                                 catch(typeError:Error)
                                 {
                                    // 忽略类型错误
                                 }
                              }
                           }
                           catch(defineError:Error)
                           {
                              // 忽略定义错误
                           }
                           
                           // 方法3: 获取所有任务并完成
                           try
                           {
                              // 获取任务数据对象
                              taskObj = Gaming.PG.da.task.dataObj;
                              if(taskObj)
                              {
                                 // 遍历所有任务
                                 for(taskName in taskObj)
                                 {
                                    taskData = taskObj[taskName];
                                    if(taskData)
                                    {
                                       try
                                       {
                                          // 如果任务未接取，先接取
                                          if(taskData.state == "no" || taskData.state == "lock")
                                          {
                                             if(taskData.getTask)
                                             {
                                                taskData.getTask();
                                             }
                                          }
                                          
                                          // 如果任务进行中，完成它
                                          if(taskData.state == "ing")
                                          {
                                             if(taskData.complete)
                                             {
                                                taskData.complete();
                                                totalCompleted++;
                                             }
                                          }
                                       }
                                       catch(taskError:Error)
                                       {
                                          // 忽略单个任务的错误，继续处理下一个
                                       }
                                    }
                                 }
                              }
                           }
                           catch(processError:Error)
                           {
                              // 忽略处理错误
                           }
                           
                           // 方法4: 使用GM命令作为备用
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("task","unlockMainTask","",0);
                              Gaming.testCtrl.cheating.doOrder("task","nowTaskComplete","",0);
                           }
                           catch(gmError:Error)
                           {
                              // GM命令失败，忽略
                           }
                           
                           // 刷新任务UI
                           try
                           {
                              if(Gaming.uiGroup.taskUI && Gaming.uiGroup.taskUI.visible)
                              {
                                 Gaming.uiGroup.taskUI.hide();
                                 TweenLite.delayedCall(0.1,function():void
                                 {
                                    Gaming.uiGroup.taskUI.show();
                                 });
                              }
                              if(Gaming.uiGroup.mainUI && Gaming.uiGroup.mainUI.fleshBtn)
                              {
                                 Gaming.uiGroup.mainUI.fleshBtn();
                              }
                           }
                           catch(uiError:Error)
                           {
                              // UI刷新失败，忽略
                           }
                           
                           result = "🎯 任务处理完成：解锁 " + totalUnlocked + " 类任务，添加 " + totalAccepted + " 个任务，完成 " + totalCompleted + " 个任务";
                        }
                        catch(e:Error)
                        {
                           result = "❌ 完成任务失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "reset":
                        Gaming.PG.da.task.clearData();
                        result = "重置任务数据";
                  }
                  break;
               case "skill":
                  switch(action)
                  {
                     case "unlock":
                        Gaming.testCtrl.cheating.doOrder("skill","unlockSkill","",0);
                        result = "解锁技能系统";
                        break loop14;
                     case "profi":
                        Gaming.testCtrl.cheating.doOrder("skill","addProfi","",v);
                        result = "增加技能熟练度: " + v;
                        break loop14;
                     case "setprofi":
                        Gaming.testCtrl.cheating.doOrder("skill","setProfi","",v);
                        result = "设置技能熟练度: " + v;
                        break loop14;
                     case "dayprofi":
                        Gaming.testCtrl.cheating.doOrder("skill","addProfi","",v);
                        result = "增加今日熟练度: " + v;
                        break loop14;
                     case "setdayprofi":
                        Gaming.testCtrl.cheating.doOrder("skill","setDayProfi","",v);
                        result = "设置今日熟练度: " + v;
                        break loop14;
                     case "clear":
                        Gaming.testCtrl.cheating.doOrder("skill","clearNowSkill","",0);
                        result = "删除当前技能";
                  }
                  break;
               case "achieve":
                  switch(action)
                  {
                     case "complete":
                        Gaming.testCtrl.cheating.doOrder("achieve","completeAllAchieve","",0);
                        result = "完成所有成就";
                        break loop14;
                     case "clear":
                        Gaming.testCtrl.cheating.doOrder("achieve","reachieve","",0);
                        result = "清空当前成就列表";
                  }
                  break;
               case "wilder":
                  switch(action)
                  {
                     case "unlock":
                        Gaming.testCtrl.cheating.doOrder("wilder","unlockAllWider","",0);
                        result = "解锁所有秘境";
                        break loop14;
                     case "bossedit":
                        Gaming.testCtrl.cheating.doOrder("wilder","ulockAllBossEditLevel","",0);
                        result = "解锁Boss编辑关卡";
                        break loop14;
                     case "setnum":
                        Gaming.testCtrl.cheating.doOrder("wilder","setNowWiderNum","",v);
                        result = "设置当前秘境使用次数: " + v;
                  }
                  break;
               case "pet":
                  switch(action)
                  {
                     case "getall":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           addedPets = 0;
                           failedPets = 0;
                           debugInfo = [];
                           
                           // 方法1: 尝试使用GM命令
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("pet","addAllPet","",0);
                              result = "✅ 通过GM命令获取所有宠物成功";
                           }
                           catch(gmError:Error)
                           {
                              debugInfo.push("GM命令失败: " + gmError.message);
                              
                              // 方法2: 手动添加基因体到基因背包
                              try
                              {
                                 // 扩展各种背包
                                 if(Gaming.PG.da.geneBag)
                                 {
                                    Gaming.PG.da.geneBag.addBagNum(200);
                                    debugInfo.push("扩展基因背包到: " + Gaming.PG.da.geneBag.saveGroup.lockLen);
                                 }
                                 if(Gaming.PG.da.pet)
                                 {
                                    Gaming.PG.da.pet.addBagNum(200);
                                    debugInfo.push("扩展宠物背包到: " + Gaming.PG.da.pet.saveGroup.lockLen);
                                 }
                                 if(Gaming.PG.da.thingsBag)
                                 {
                                    Gaming.PG.da.thingsBag.addBagNum(200);
                                 }
                                 
                                 // 获取宠物名称列表
                                 normalPetNames = [];
                                 try
                                 {
                                    normalPetNames = Gaming.defineGroup.gene.getNormalNameArr();
                                    debugInfo.push("方法1: 找到 " + normalPetNames.length + " 个基础宠物");
                                 }
                                 catch(e1:Error)
                                 {
                                    debugInfo.push("方法1失败: " + e1.message);
                                 }
                                 
                                 if(normalPetNames.length == 0)
                                 {
                                    normalPetNames = ["BoomSkull","IronChief","Lake","FightWolf","ZombieWolf","ZombieKing","ZombieCleaver","IronChiefSecond","Laer","PetLake","BoomWolf","ZombieChief","FireWolf","IceWolf","ThunderWolf","PoisonWolf","WindWolf"];
                                    debugInfo.push("方法2: 使用备用宠物列表 " + normalPetNames.length + " 个");
                                 }
                                 
                                 // 如果还是没有，尝试从定义组获取
                                 if(normalPetNames.length == 0)
                                 {
                                    try
                                    {
                                       if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.obj)
                                       {
                                          for(var geneId:String in Gaming.defineGroup.gene.obj)
                                          {
                                             var geneDef:* = Gaming.defineGroup.gene.obj[geneId];
                                             if(geneDef && geneDef.cnName)
                                             {
                                                normalPetNames.push(geneId);
                                                if(normalPetNames.length >= 20)
                                                {
                                                   break;
                                                }
                                             }
                                          }
                                          debugInfo.push("方法3: 从定义组获取 " + normalPetNames.length + " 个宠物");
                                       }
                                    }
                                    catch(e3:Error)
                                    {
                                       debugInfo.push("方法3失败: " + e3.message);
                                    }
                                 }
                                 
                                 // 添加宠物/基因体
                                 for each(petName in normalPetNames)
                                 {
                                    var success:Boolean = false;
                                    
                                    try
                                    {
                                       // 检查定义是否存在
                                       geneDefine = Gaming.defineGroup.gene.getDefine(petName);
                                       if(!geneDefine)
                                       {
                                          debugInfo.push("跳过未定义宠物: " + petName);
                                          failedPets++;
                                          continue;
                                       }
                                       
                                       // 方法A: 直接添加到基因背包
                                       try
                                       {
                                          if(Gaming.PG.da.geneBag)
                                          {
                                             if(Gaming.PG.da.geneBag.getSpaceNum() <= 0)
                                             {
                                                Gaming.PG.da.geneBag.addBagNum(10);
                                             }
                                             var geneItemData:* = Gaming.PG.da.geneBag.addByName(petName, 1);
                                             if(geneItemData)
                                             {
                                                addedPets++;
                                                debugInfo.push("✅ 基因背包添加: " + petName);
                                                success = true;
                                             }
                                          }
                                       }
                                       catch(eA:Error)
                                       {
                                          debugInfo.push("方法A失败 " + petName + ": " + eA.message);
                                       }
                                       
                                       // 方法B: 添加到物品背包
                                       if(!success)
                                       {
                                          try
                                          {
                                             if(Gaming.PG.da.thingsBag)
                                             {
                                                if(Gaming.PG.da.thingsBag.getSpaceNum() <= 0)
                                                {
                                                   Gaming.PG.da.thingsBag.addBagNum(10);
                                                }
                                                var thingItemData:* = Gaming.PG.da.thingsBag.addByName(petName, 1);
                                                if(thingItemData)
                                                {
                                                   addedPets++;
                                                   debugInfo.push("✅ 物品背包添加: " + petName);
                                                   success = true;
                                                }
                                             }
                                          }
                                          catch(eB:Error)
                                          {
                                             debugInfo.push("方法B失败 " + petName + ": " + eB.message);
                                          }
                                       }
                                       
                                       // 方法C: 直接创建宠物
                                       if(!success)
                                       {
                                          try
                                          {
                                             if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                             {
                                                Gaming.PG.da.pet.addBagNum(20);
                                             }
                                             geneSave = Gaming.defineGroup.geneCreator.getSave("red", 1, petName, true);
                                             if(geneSave)
                                             {
                                                geneData = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                                if(geneData)
                                                {
                                                   petData = Gaming.PG.da.pet.addByGeneData(geneData);
                                                   if(petData)
                                                   {
                                                      addedPets++;
                                                      debugInfo.push("✅ 直接创建宠物: " + petName);
                                                      success = true;
                                                   }
                                                }
                                             }
                                          }
                                          catch(eC:Error)
                                          {
                                             debugInfo.push("方法C失败 " + petName + ": " + eC.message);
                                          }
                                       }
                                       
                                       if(!success)
                                       {
                                          failedPets++;
                                          debugInfo.push("❌ 所有方法都失败: " + petName);
                                       }
                                    }
                                    catch(petError:Error)
                                    {
                                       failedPets++;
                                       debugInfo.push("❌ 宠物异常 " + petName + ": " + petError.message);
                                    }
                                 }
                                 
                                 // 刷新UI
                                 try
                                 {
                                    if(Gaming.uiGroup.geneBagUI && Gaming.uiGroup.geneBagUI.visible)
                                    {
                                       Gaming.uiGroup.geneBagUI.fleshData();
                                    }
                                    if(Gaming.uiGroup.thingsBagUI && Gaming.uiGroup.thingsBagUI.visible)
                                    {
                                       Gaming.uiGroup.thingsBagUI.fleshData();
                                    }
                                    if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                                    {
                                       Gaming.uiGroup.petUI.fleshData();
                                    }
                                 }
                                 catch(uiError:Error)
                                 {
                                    debugInfo.push("UI刷新失败: " + uiError.message);
                                 }
                                 
                                 result = "🧬 添加宠物/基因体完成：成功 " + addedPets + " 个，失败 " + failedPets + " 个";
                                 result += "\n💡 提示：基因体已添加到背包，可以孵化获得宠物";
                                 if(debugInfo.length > 0)
                                 {
                                    result += "\n📋 调试信息:\n" + debugInfo.slice(0,15).join("\n");
                                 }
                              }
                              catch(manualError:Error)
                              {
                                 result = "❌ 手动添加宠物失败: " + manualError.message + "\n调试信息:\n" + debugInfo.join("\n");
                              }
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 获取宠物失败: " + e.message + "\n堆栈: " + e.getStackTrace();
                           break loop14;
                        }
                        break loop14;
                     case "getsafe":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           Gaming.PG.da.pet.addBagNum(50);
                           safeAddedPets = 0;
                           safeFailedPets = 0;
                           safeDebugInfo = [];
                           safePetNames = ["BoomSkull","IronChief","Lake","FightWolf","ZombieWolf"];
                           for each(safePetName in safePetNames)
                           {
                              try
                              {
                                 if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                 {
                                    Gaming.PG.da.pet.addBagNum(5);
                                 }
                                 safeGeneDefine = Gaming.defineGroup.gene.getDefine(safePetName);
                                 if(!safeGeneDefine)
                                 {
                                    safeDebugInfo.push("跳过未定义的宠物: " + safePetName);
                                    safeFailedPets++;
                                    continue;
                                 }
                                 geneSave = Gaming.defineGroup.geneCreator.getSave("red",1,safePetName,true);
                                 if(geneSave)
                                 {
                                    geneData = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                    if(geneData)
                                    {
                                       petData = Gaming.PG.da.pet.addByGeneData(geneData);
                                       if(petData)
                                       {
                                          safeAddedPets++;
                                          safeDebugInfo.push("成功添加: " + safePetName);
                                       }
                                       else
                                       {
                                          safeFailedPets++;
                                          safeDebugInfo.push("添加失败: " + safePetName + " (addByGeneData返回null)");
                                       }
                                    }
                                    else
                                    {
                                       safeFailedPets++;
                                       safeDebugInfo.push("添加失败: " + safePetName + " (getTempData返回null)");
                                    }
                                 }
                                 else
                                 {
                                    safeFailedPets++;
                                    safeDebugInfo.push("添加失败: " + safePetName + " (getSave返回null)");
                                 }
                              }
                              catch(safeError:Error)
                              {
                                 safeFailedPets++;
                                 safeDebugInfo.push("添加异常: " + safePetName + " - " + safeError.message);
                              }
                           }
                           try
                           {
                              if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                              {
                                 Gaming.uiGroup.petUI.fleshData();
                              }
                           }
                           catch(uiError:Error)
                           {
                              safeDebugInfo.push("UI刷新失败: " + uiError.message);
                           }
                           result = "✅ 安全添加宠物：成功 " + safeAddedPets + " 只，失败 " + safeFailedPets + " 只\n调试信息:\n" + safeDebugInfo.join("\n");
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "❌ 安全添加宠物失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "getfew":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           Gaming.PG.da.pet.addBagNum(20);
                           fewAddedPets = 0;
                           fewFailedPets = 0;
                           fewDebugInfo = [];
                           fewPetNames = ["BoomSkull","IronChief","Lake"];
                           for each(fewPetName in fewPetNames)
                           {
                              try
                              {
                                 if(Gaming.PG.da.pet.getSpaceNum() <= 0)
                                 {
                                    Gaming.PG.da.pet.addBagNum(5);
                                 }
                                 fewGeneDefine = Gaming.defineGroup.gene.getDefine(fewPetName);
                                 if(!fewGeneDefine)
                                 {
                                    fewDebugInfo.push("跳过未定义的宠物: " + fewPetName);
                                    fewFailedPets++;
                                    continue;
                                 }
                                 geneSave = Gaming.defineGroup.geneCreator.getSave("red",1,fewPetName,true);
                                 if(geneSave)
                                 {
                                    geneData = Gaming.defineGroup.geneCreator.getTempData(geneSave);
                                    if(geneData)
                                    {
                                       petData = Gaming.PG.da.pet.addByGeneData(geneData);
                                       if(petData)
                                       {
                                          fewAddedPets++;
                                          fewDebugInfo.push("成功添加: " + fewPetName);
                                       }
                                       else
                                       {
                                          fewFailedPets++;
                                          fewDebugInfo.push("添加失败: " + fewPetName + " (addByGeneData返回null)");
                                       }
                                    }
                                    else
                                    {
                                       fewFailedPets++;
                                       fewDebugInfo.push("添加失败: " + fewPetName + " (getTempData返回null)");
                                    }
                                 }
                                 else
                                 {
                                    fewFailedPets++;
                                    fewDebugInfo.push("添加失败: " + fewPetName + " (getSave返回null)");
                                 }
                              }
                              catch(fewError:Error)
                              {
                                 fewFailedPets++;
                                 fewDebugInfo.push("添加异常: " + fewPetName + " - " + fewError.message);
                              }
                           }
                           try
                           {
                              if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                              {
                                 Gaming.uiGroup.petUI.fleshData();
                              }
                           }
                           catch(uiError:Error)
                           {
                              fewDebugInfo.push("UI刷新失败: " + uiError.message);
                           }
                           result = "✅ 简化添加宠物：成功 " + fewAddedPets + " 只，失败 " + fewFailedPets + " 只\n调试信息:\n" + fewDebugInfo.join("\n");
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "❌ 简化添加宠物失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "debug":
                        try
                        {
                           debugResult = [];
                           debugResult.push("=== 基因定义组检查 ===");
                           if(Gaming.defineGroup && Gaming.defineGroup.gene)
                           {
                              debugResult.push("✅ 基因定义组存在");
                              normalNames = Gaming.defineGroup.gene.getNormalNameArr();
                              debugResult.push("正常宠物数量: " + normalNames.length);
                              debugResult.push("前10个宠物: " + normalNames.slice(0,10).join(", "));
                              testPets = ["BoomSkull","IronChief","Lake","FightWolf","ZombieWolf"];
                              debugResult.push("\n=== 测试宠物定义 ===");
                              for each(testPet in testPets)
                              {
                                 testDefine = Gaming.defineGroup.gene.getDefine(testPet);
                                 if(testDefine)
                                 {
                                    debugResult.push("✅ " + testPet + ": 定义存在，中文名: " + testDefine.cnName);
                                 }
                                 else
                                 {
                                    debugResult.push("❌ " + testPet + ": 定义不存在");
                                 }
                              }
                           }
                           else
                           {
                              debugResult.push("❌ 基因定义组不存在");
                           }
                           debugResult.push("\n=== 基因创建器检查 ===");
                           if(Gaming.defineGroup && Gaming.defineGroup.geneCreator)
                           {
                              debugResult.push("✅ 基因创建器存在");
                              try
                              {
                                 testGeneSave = Gaming.defineGroup.geneCreator.getSave("red",1,"BoomSkull",true);
                                 if(testGeneSave)
                                 {
                                    debugResult.push("✅ 测试创建基因保存成功");
                                    testGeneData = Gaming.defineGroup.geneCreator.getTempData(testGeneSave);
                                    if(testGeneData)
                                    {
                                       debugResult.push("✅ 测试创建基因数据成功");
                                    }
                                    else
                                    {
                                       debugResult.push("❌ 测试创建基因数据失败");
                                    }
                                 }
                                 else
                                 {
                                    debugResult.push("❌ 测试创建基因保存失败");
                                 }
                              }
                              catch(testError:Error)
                              {
                                 debugResult.push("❌ 测试创建异常: " + testError.message);
                              }
                           }
                           else
                           {
                              debugResult.push("❌ 基因创建器不存在");
                           }
                           debugResult.push("\n=== 宠物背包检查 ===");
                           if(Gaming.PG && Gaming.PG.da && Gaming.PG.da.pet)
                           {
                              debugResult.push("✅ 宠物背包存在");
                              debugResult.push("当前宠物数量: " + Gaming.PG.da.pet.arr.length);
                              debugResult.push("背包容量: " + Gaming.PG.da.pet.saveGroup.lockLen);
                              debugResult.push("剩余空间: " + Gaming.PG.da.pet.getSpaceNum());
                           }
                           else
                           {
                              debugResult.push("❌ 宠物背包不存在");
                           }
                           result = debugResult.join("\n");
                           break loop14;
                        }
                        catch(debugError:Error)
                        {
                           result = "❌ 调试检查失败: " + debugError.message;
                           break loop14;
                        }
                        break;
                     case "expandbag":
                        try
                        {
                           Gaming.PG.da.pet.addBagNum(100);
                           result = "宠物格子已扩展到 " + Gaming.PG.da.pet.saveGroup.lockLen + " 个！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "扩展宠物格子失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "getallcards":
                        Gaming.testCtrl.cheating.doOrder("achieve","createBosscard","所有魂卡 5",0);
                        result = "添加所有5星魂卡";
                        break loop14;
                     case "craftexp":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("pet","addNowCraftExp","",v);
                           result = "增加飞船经验: " + v;
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "增加飞船经验失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "craftlv":
                        try
                        {
                           Gaming.testCtrl.cheating.doOrder("pet","setNowCraftLv","",v);
                           result = "设置飞船等级: " + v;
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "设置飞船等级失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "setlv":
                        Gaming.testCtrl.cheating.doOrder("pet","setPetLv","",v);
                        result = "设置当前宠物等级: " + v;
                        break loop14;
                     case "craftexp":
                        Gaming.testCtrl.cheating.doOrder("pet","addNowCraftExp","",v);
                        result = "设置飞船经验: " + v;
                        break loop14;
                     case "craftlv":
                        Gaming.testCtrl.cheating.doOrder("pet","setNowCraftLv","",v);
                        result = "设置飞船等级: " + v;
                        break loop14;
                     case "testsingle":
                        try
                        {
                           Gaming.PG.da.pet.addBagNum(10);
                           testPetName = "BoomSkull";
                           testGeneDefine = Gaming.defineGroup.gene.getDefine(testPetName);
                           if(testGeneDefine)
                           {
                              testGeneSave = Gaming.defineGroup.geneCreator.getSave("red",Gaming.PG.da.level,testPetName,true);
                              if(testGeneSave)
                              {
                                 testGeneData = Gaming.defineGroup.geneCreator.getTempData(testGeneSave);
                                 if(testGeneData)
                                 {
                                    testPetData = Gaming.PG.da.pet.addByGeneData(testGeneData);
                                    if(testPetData)
                                    {
                                       if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
                                       {
                                          Gaming.uiGroup.petUI.fleshData();
                                       }
                                       result = "成功添加测试宠物: " + testGeneDefine.cnName + " (ID: " + testPetName + ")";
                                    }
                                    else
                                    {
                                       result = "添加宠物失败: addByGeneData返回null";
                                    }
                                 }
                                 else
                                 {
                                    result = "创建基因数据失败: getTempData返回null";
                                 }
                              }
                              else
                              {
                                 result = "创建基因保存失败: getSave返回null";
                              }
                           }
                           else
                           {
                              result = "找不到宠物定义: " + testPetName;
                           }
                        }
                        catch(testError:Error)
                        {
                           result = "测试宠物添加失败: " + testError.message + " (堆栈: " + testError.getStackTrace() + ")";
                           break loop14;
                        }
                        break;
                     case "addbag":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           Gaming.PG.da.pet.addBagNum(v);
                           result = "成功扩展尸宠背包 " + v + " 个格子！当前背包容量: " + Gaming.PG.da.pet.saveGroup.lockLen;
                        }
                        catch(e:Error)
                        {
                           result = "扩展尸宠背包失败: " + e.message;
                        }
                        break loop14;
                  }
                  break;
               case "more":
                  switch(action)
                  {
                     case "addgirl":
                        Gaming.testCtrl.cheating.doOrder("more","addMore","",0);
                        result = "添加妞";
                  }
                  break;
               case "pay":
                  switch(action)
                  {
                     case "gold":
                        Gaming.testCtrl.cheating.doOrder("pay","testYue","",v);
                        result = "设置黄金: " + v;
                        break loop14;
                     case "recharge":
                        Gaming.testCtrl.cheating.doOrder("pay","pay","",v);
                        result = "充值: " + v;
                        break loop14;
                     case "showcount":
                        Gaming.testCtrl.cheating.doOrder("pay","showPayCount","",0);
                        result = "显示充值统计";
                  }
                  break;
               case "arena":
                  switch(action)
                  {
                     case "num":
                        Gaming.PG.save.arena.todayNum = v;
                        result = "设置竞技场次数: " + v;
                        break loop14;
                     case "score":
                        Gaming.PG.save.arena.score = v;
                        result = "设置竞技场分数: " + v;
                  }
                  break;
               case "space":
                  switch(action)
                  {
                     case "level":
                        craft = Gaming.PG.da.space.craft.getNowData();
                        if(craft)
                        {
                           craft.setLevel(v);
                           result = "设置飞船等级: " + v;
                           break loop14;
                        }
                        result = "没有找到飞船数据";
                        break loop14;
                     case "exp":
                        Gaming.PG.da.space.addNowCraftExp(v);
                        result = "增加飞船经验: " + v;
                  }
                  break;
               case "union":
                  switch(action)
                  {
                     case "rank":
                        militaryDef = Gaming.defineGroup.union.military.getDefine(v + "");
                        if(militaryDef)
                        {
                           Gaming.testCtrl.cheating.tempContribution = militaryDef.totalMust;
                           result = "设置军衔等级: " + v + " (" + militaryDef.cnName + ")";
                           break loop14;
                        }
                        result = "军衔等级 " + v + " 不存在";
                        break loop14;
                     case "army":
                        Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),v + "");
                        result = "快速升级军队到: " + v + "级";
                        break loop14;
                     case "contribution":
                        result = "贡献值功能需要具体数值";
                  }
                  break;
               case "achieve":
                  switch(action)
                  {
                     case "unlockall":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           completedCount = 0;
                           errorCount = 0;
                           
                           // 方法1: 尝试使用GM命令
                           try
                           {
                              Gaming.testCtrl.cheating.doOrder("achieve","completeAllAchieve","",0);
                              result = "✅ 通过GM命令解锁全部成就成功";
                           }
                           catch(gmError:Error)
                           {
                              // 方法2: 尝试使用成就系统的completeAll方法
                              try
                              {
                                 if(Gaming.PG && Gaming.PG.da && Gaming.PG.da.achieve)
                                 {
                                    Gaming.PG.da.achieve.completeAll();
                                    Gaming.PG.da.achieve.fleshAffterComplete();
                                    result = "✅ 通过成就系统解锁全部成就成功";
                                 }
                                 else
                                 {
                                    throw new Error("成就系统不存在");
                                 }
                              }
                              catch(systemError:Error)
                              {
                                 // 方法3: 手动遍历所有成就并完成
                                 try
                                 {
                                    achieveGroup = Gaming.PG.da.achieve;
                                    if(achieveGroup && achieveGroup.obj)
                                    {
                                       allAchievements = achieveGroup.obj;
                                       for(achieveName in allAchievements)
                                       {
                                          achieveData = allAchievements[achieveName];
                                          if(achieveData)
                                          {
                                             try
                                             {
                                                if(!achieveData.isCompleteB())
                                                {
                                                   // 尝试多种完成方法
                                                   if(achieveData.save)
                                                   {
                                                      achieveData.save.state = "complete";
                                                      achieveData.newB = true;
                                                   }
                                                   achieveData.complete();
                                                   completedCount++;
                                                }
                                             }
                                             catch(completeError:Error)
                                             {
                                                errorCount++;
                                                continue;
                                             }
                                          }
                                       }
                                       // 刷新成就系统
                                       try
                                       {
                                          achieveGroup.fleshAffterComplete();
                                       }
                                       catch(fleshError:Error)
                                       {
                                       }
                                       result = "✅ 手动完成 " + completedCount + " 个成就" + (errorCount > 0 ? "，失败 " + errorCount + " 个" : "");
                                    }
                                    else
                                    {
                                       result = "❌ 无法访问成就数据";
                                    }
                                 }
                                 catch(manualError:Error)
                                 {
                                    result = "❌ 手动完成成就失败: " + manualError.message;
                                 }
                              }
                           }
                           
                           // 刷新成就UI
                           try
                           {
                              if(Gaming.uiGroup.achieveUI && Gaming.uiGroup.achieveUI.visible)
                              {
                                 Gaming.uiGroup.achieveUI.hide();
                                 TweenLite.delayedCall(0.1,function():void
                                 {
                                    Gaming.uiGroup.achieveUI.show();
                                 });
                              }
                           }
                           catch(uiError:Error)
                           {
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 解锁成就失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "forceall":
                        try
                        {
                           Gaming.testCtrl.enabled = true;
                           Gaming.testCtrl.cheating.enabled = true;
                           achieveGroup = Gaming.PG.da.achieve;
                           completedCount = 0;
                           errorCount = 0;
                           try
                           {
                              achieveGroup.completeAll();
                              achieveGroup.fleshAffterComplete();
                              result = "🎉 通过系统方法强制完成所有成就";
                           }
                           catch(systemError:Error)
                           {
                              allAchievements = achieveGroup.obj;
                              for(achieveName in allAchievements)
                              {
                                 achieveData = allAchievements[achieveName];
                                 if(achieveData)
                                 {
                                    try
                                    {
                                       if(!achieveData.isCompleteB())
                                       {
                                          if(achieveData.save)
                                          {
                                             achieveData.save.state = "complete";
                                             achieveData.newB = true;
                                          }
                                          else
                                          {
                                             achieveData.complete();
                                          }
                                          completedCount++;
                                       }
                                    }
                                    catch(achieveError:Error)
                                    {
                                       errorCount++;
                                       continue;
                                    }
                                 }
                              }
                              try
                              {
                                 achieveGroup.fleshAffterComplete();
                              }
                              catch(fleshError:Error)
                              {
                              }
                              result = "🎉 强制完成 " + completedCount + " 个成就，失败 " + errorCount + " 个";
                           }
                           try
                           {
                              if(Gaming.uiGroup.achieveUI && Gaming.uiGroup.achieveUI.visible)
                              {
                                 Gaming.uiGroup.achieveUI.hide();
                                 TweenLite.delayedCall(0.1,function():void
                                 {
                                    Gaming.uiGroup.achieveUI.show();
                                 });
                              }
                           }
                           catch(uiError:Error)
                           {
                           }
                        }
                        catch(e:Error)
                        {
                           result = "❌ 强制完成成就失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "kill":
                        Gaming.PG.SAVE.count.killNum = v;
                        result = "设置杀敌数: " + v;
                        break loop14;
                     case "noorange":
                        Gaming.PG.SAVE.count.bossNoOrangeNum = v;
                        result = "设置不掉橙装数: " + v;
                        break loop14;
                     case "king":
                        Gaming.PG.SAVE.count.maxKingLevel = v;
                        result = "设置擒王等级: " + v;
                        break loop14;
                     case "coin":
                        Gaming.PG.SAVE.count.lotteryCoin = v;
                        result = "设置全是银币次数: " + v;
                        break loop14;
                     case "orange":
                        Gaming.PG.save.count.lotteryAllOrangeNum = v;
                        result = "设置全是橙装次数: " + v;
                        break loop14;
                     case "reset":
                        Gaming.PG.da.achieve.clearData();
                        result = "重置成就数据";
                  }
                  break;
               case "time":
                  switch(action)
                  {
                     case "speed":
                        CountCtrl.onlineTimePer = v;
                        result = "设置时间加速倍数: " + v;
                  }
                  break;
               case "character":
                  switch(action)
                  {
                     case "level99":
                        Gaming.testCtrl.cheating.doOrder("player","setHeroLv","",99);
                        result = "设置角色等级99";
                        break loop14;
                     case "maxlove":
                        try
                        {
                           moreArr = Gaming.PG.da.more.dataArr;
                           moreBagArr = Gaming.PG.da.moreBag.dataArr;
                           allMoreArr = moreArr.concat(moreBagArr);
                           setCount = 0;
                           for each(moreData in allMoreArr)
                           {
                              if(moreData && moreData.love)
                              {
                                 loveData = moreData.love;
                                 loveData.addValue(9999 - loveData.getValue());
                                 setCount++;
                              }
                           }
                           result = "设置了 " + setCount + " 个角色的满好感";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "设置好感度失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "maxstats":
                        Gaming.PG.da.peak.save.lv = 999;
                        result = "设置满属性";
                        break loop14;
                     case "reset":
                        Gaming.PG.da.love.clearData();
                        result = "重置角色数据";
                        break loop14;
                     case "infinitepower":
                        try
                        {
                           moreArr2 = Gaming.PG.da.more.dataArr;
                           moreBagArr2 = Gaming.PG.da.moreBag.dataArr;
                           allMoreArr2 = moreArr2.concat(moreBagArr2);
                           powerCount = 0;
                           for each(moreData2 in allMoreArr2)
                           {
                              if(moreData2 && moreData2.partner && moreData2.partner.save)
                              {
                                 moreData2.partner.save.power = 999;
                                 powerCount++;
                              }
                           }
                           result = "设置了 " + powerCount + " 个角色的无限体力";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "设置体力失败: " + e.message;
                           break loop14;
                        }
                  }
                  break;
               case "other":
                  switch(action)
                  {
                     case "exploit":
                        Gaming.testCtrl.cheating.doOrder("other","setPartnerExploit","",v);
                        result = "设置队友功勋: " + v;
                        break loop14;
                     case "clearpets":
                        try
                        {
                           Gaming.PG.da.pet.clearData();
                           result = "清除尸宠背包成功！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "清除尸宠背包失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "setcheat":
                        try
                        {
                           Gaming.PG.save.main.isZuobiB = true;
                           result = "设置存档异常成功！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "设置存档异常失败: " + e.message;
                           break loop14;
                        }
                        break;
                     case "clearoverflow":
                        try
                        {
                           Gaming.PG.DATA.arms.delNoPositionItems();
                           result = "清除溢出物品成功！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "清除溢出物品失败: " + e.message;
                           break loop14;
                        }
                  }
                  break;
               case "time":
                  switch(action)
                  {
                     case "clearall":
                        try
                        {
                           Gaming.PG.da.time.clearAllTime();
                           result = "清除所有双倍时间成功！";
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "清除所有双倍时间失败: " + e.message;
                           break loop14;
                        }
                  }
                  break;
               case "custom":
                  switch(action)
                  {
                     case "addsystem":
                        showCustomAddSystem();
                        result = "打开自定义添加系统";
                        break loop14;
                     case "card":
                        showCustomCardEditor();
                        result = "🎴 打开自定义魂卡属性编辑器";
                        break loop14;
                     case "equip":
                        showCustomEquipEditor();
                        result = "⚔️ 打开自定义装备属性编辑器";
                        break loop14;
                     case "weapon":
                        showCustomWeaponEditor();
                        result = "🔫 打开自定义武器属性编辑器";
                        break loop14;
                     case "vehicle":
                        showCustomVehicleEditor();
                        result = "🚗 打开自定义载具属性编辑器";
                        break loop14;
                     case "skill":
                        showCustomSkillEditor();
                        result = "✨ 打开自定义技能属性编辑器";
                        break loop14;
                     case "getcard":
                        getCustomCard();
                        result = "🎁 获取自定义魂卡";
                        break loop14;
                     case "getequip":
                        getCustomEquip();
                        result = "📦 获取自定义装备";
                        break loop14;
                     case "getweapon":
                        getCustomWeapon();
                        result = "🎯 获取自定义武器";
                        break loop14;
                     case "getvehicle":
                        getCustomVehicle();
                        result = "🚀 获取自定义载具";
                        break loop14;
                     case "getskill":
                        getCustomSkill();
                        result = "⭐ 获取自定义技能";
                  }
                  break;
               case "export":
                  switch(action)
                  {
                     case "allids":
                        result = "导出功能：请查看剪贴板";
                        try
                        {
                           exportAllGameIDs();
                           break loop14;
                        }
                        catch(e:Error)
                        {
                           result = "导出失败: " + e.message;
                           break loop14;
                        }
                  }
                  break;
               case "tower":
                  switch(action)
                  {
                     case "complete":
                        try
                        {
                           towerData = Gaming.PG.da.tower;
                           if(towerData && towerData.getSave())
                           {
                              towerSave = towerData.getSave();
                              towerDefines = Gaming.defineGroup.tower.getUIArr();
                              completedCount = 0;
                              for each(towerDefine in towerDefines)
                              {
                                 if(towerDefine && towerDefine.getName)
                                 {
                                    towerName = towerDefine.getName();
                                    maxDiff = 4;
                                    towerSave.winEvent(towerName,maxDiff);
                                    towerSave.giftEvent(towerName,maxDiff);
                                    completedCount++;
                                 }
                              }
                              unendData = towerData.unend;
                              if(unendData && unendData.getSave())
                              {
                                 unendSave = unendData.getSave();
                                 maxUnendLevel = 95;
                                 unendSave.unendLv = maxUnendLevel;
                                 unendSave.uP = 0;
                                 result = "🗼 虚天塔通关成功！已通关 " + completedCount + " 个幻塔层，天塔已通关至第 " + maxUnendLevel + " 层！";
                              }
                              else
                              {
                                 result = "🗼 幻塔通关成功！已通关 " + completedCount + " 个塔层，但天塔数据不可用！";
                              }
                           }
                           else
                           {
                              result = "🗼 虚天塔数据不可用，请先进入游戏";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "🗼 通关虚天塔失败: " + e.message;
                           break loop14;
                        }
                        break loop14;
                     case "all":
                        try
                        {
                           towerData = Gaming.PG.da.tower;
                           if(towerData && towerData.getSave())
                           {
                              towerSave = towerData.getSave();
                              towerDefines = Gaming.defineGroup.tower.getUIArr();
                              completedCount = 0;
                              for each(towerDefine in towerDefines)
                              {
                                 if(towerDefine && towerDefine.getName)
                                 {
                                    towerName = towerDefine.getName();
                                    maxDiff = 4;
                                    towerSave.winEvent(towerName,maxDiff);
                                    towerSave.giftEvent(towerName,maxDiff);
                                    completedCount++;
                                 }
                              }
                              unendData = towerData.unend;
                              if(unendData && unendData.getSave())
                              {
                                 unendSave = unendData.getSave();
                                 maxUnendLevel = 95;
                                 unendSave.unendLv = maxUnendLevel;
                                 unendSave.uP = 0;
                                 result = "🗼 一键通关虚天塔成功！已通关 " + completedCount + " 个幻塔层，天塔已通关至第 " + maxUnendLevel + " 层，全部设为最高难度！";
                              }
                              else
                              {
                                 result = "🗼 一键通关幻塔成功！已通关 " + completedCount + " 个塔层，全部设为最高难度，但天塔数据不可用！";
                              }
                           }
                           else
                           {
                              result = "🗼 虚天塔数据不可用，请先进入游戏";
                           }
                        }
                        catch(e:Error)
                        {
                           result = "🗼 通关虚天塔失败: " + e.message;
                           break loop14;
                        }
                  }
            }
            addOutput(result);
         }
         catch(error:Error)
         {
            addOutput("执行错误: " + error.message);
         }
      }
      
      public function show() : void
      {
         if(!isVisible)
         {
            isVisible = true;
            this.visible = true;
            this.x = (Gaming.WIDTH - 900) / 2;
            this.y = (Gaming.HEIGHT - 600) / 2;
         }
      }
      
      public function hide(e:MouseEvent = null) : void
      {
         if(isVisible)
         {
            isVisible = false;
            this.visible = false;
         }
      }
      
      public function toggle() : void
      {
         if(isVisible)
         {
            hide();
         }
         else
         {
            show();
         }
      }
      
      public function handleKeyDown(e:KeyboardEvent) : void
      {
         if(!isVisible)
         {
            return;
         }
         if(e.keyCode == Keyboard.ESCAPE)
         {
            if(inputContainer && inputContainer.visible)
            {
               inputContainer.visible = false;
               addOutput("❌ 取消输入");
            }
         }
         else if(e.keyCode == Keyboard.ENTER)
         {
            if(inputContainer && inputContainer.visible)
            {
               onInputConfirm(null);
            }
         }
      }
      
      private function addOutput(text:String) : void
      {
         outputText.appendText(text + "\n");
         outputText.scrollV = outputText.maxScrollV;
      }
      
      private function exportAllGameIDs() : void
      {
         var exportText:String;
         var groupName:String;
         var group:*;
         var count:int;
         var id:String;
         var basicInfo:String;
         try
         {
            exportText = "=== 游戏完整ID数据导出 ===\n";
            exportText += "导出时间: " + new Date().toString() + "\n\n";
            exportText += this.exportCategoryIDs("物品 Things","things");
            exportText += this.exportCategoryIDs("装备 Equipment","equip");
            exportText += this.exportCategoryIDs("武器 Arms","bullet");
            exportText += this.exportCategoryIDs("宠物/基因 Pets/Genes","gene");
            exportText += this.exportCategoryIDs("技能 Skills","skill");
            exportText += this.exportCategoryIDs("零件 Parts","parts");
            exportText += this.exportCategoryIDs("装置 Devices","device");
            exportText += this.exportCategoryIDs("兵器 Weapons","weapon");
            exportText += this.exportFashionIDs();
            exportText += this.exportMapIDs();
            exportText += this.exportEnemyIDs();
            exportText += this.exportCategoryIDs("礼品 Gifts","gift");
            exportText += this.exportCategoryIDs("成就 Achievements","achieve");
            exportText += this.exportCategoryIDs("任务 Missions","mission");
            exportText += this.exportCategoryIDs("商店 Shops","shop");
            exportText += this.exportSoundIDs();
            exportText += this.exportUIIDs();
            exportText += this.exportAllDefineGroups();
            try
            {
               if(Gaming.defineGroup)
               {
                  exportText += "defineGroup 可用\n";
                  for(groupName in Gaming.defineGroup)
                  {
                     try
                     {
                        group = Gaming.defineGroup[groupName];
                        if(group)
                        {
                           exportText += "定义组: " + groupName + " - 可用\n";
                           if(group.obj)
                           {
                              count = 0;
                              for(id in group.obj)
                              {
                                 if(count >= 5)
                                 {
                                    break;
                                 }
                                 exportText += "  示例ID: " + id + "\n";
                                 count++;
                              }
                           }
                           if(group.arr && group.arr.length)
                           {
                              exportText += "  数组长度: " + group.arr.length + "\n";
                           }
                        }
                     }
                     catch(e:Error)
                     {
                        exportText += "定义组: " + groupName + " - 访问错误: " + e.message + "\n";
                     }
                  }
               }
               else
               {
                  exportText += "Gaming.defineGroup 不可用\n";
               }
            }
            catch(e:Error)
            {
               exportText += "访问defineGroup时出错: " + e.message + "\n";
            }
            exportText += "\n=== 导出完成 ===\n";
            exportText += "基础数据导出完成\n";
            System.setClipboard(exportText);
            addOutput("✅ 基础数据已导出并复制到剪贴板");
            addOutput("� 可以粘贴查看详细信息");
         }
         catch(error:Error)
         {
            addOutput("❌ 导出失败: " + error.message);
            basicInfo = "=== 基础信息 ===\n";
            basicInfo += "导出时间: " + new Date().toString() + "\n";
            basicInfo += "错误信息: " + error.message + "\n";
            System.setClipboard(basicInfo);
         }
      }
      
      private function exportCategoryIDs(categoryName:String, groupKey:String) : String
      {
         var group:*;
         var count:int;
         var id:String;
         var def:*;
         var cnName:String;
         var i:int;
         var arrDef:*;
         var arrId:String;
         var arrCnName:String;
         var result:String = "\n=== " + categoryName + " ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup[groupKey])
            {
               group = Gaming.defineGroup[groupKey];
               count = 0;
               if(group.obj)
               {
                  for(id in group.obj)
                  {
                     try
                     {
                        def = group.obj[id];
                        cnName = "未知";
                        if(def && def.cnName)
                        {
                           cnName = def.cnName;
                        }
                        result += "ID: " + id + " | 名称: " + cnName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + id + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }
               if(group.arr && group.arr.length > 0)
               {
                  i = 0;
                  while(i < group.arr.length)
                  {
                     try
                     {
                        arrDef = group.arr[i];
                        if(arrDef)
                        {
                           arrId = arrDef.name || arrDef.id || "index_" + i;
                           arrCnName = arrDef.cnName || true;
                           result += "ID: " + arrId + " | 名称: " + arrCnName + "\n";
                           count++;
                        }
                     }
                     catch(e:Error)
                     {
                        result += "ID: index_" + i + " | 名称: 解析错误\n";
                        count++;
                     }
                     i++;
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += categoryName + " 数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += categoryName + " 导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportFashionIDs() : String
      {
         var count:int;
         var fashionId:String;
         var fashionDef:*;
         var fashionName:String;
         var result:String = "\n=== 时装 Fashion ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.fashionObj)
            {
               count = 0;
               for(fashionId in Gaming.defineGroup.equip.fashionObj)
               {
                  try
                  {
                     fashionDef = Gaming.defineGroup.equip.fashionObj[fashionId];
                     fashionName = fashionDef && fashionDef.cnName ? fashionDef.cnName : "未知";
                     result += "ID: " + fashionId + " | 名称: " + fashionName + "\n";
                     count++;
                  }
                  catch(e:Error)
                  {
                     result += "ID: " + fashionId + " | 名称: 解析错误\n";
                     count++;
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "时装数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "时装导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportMapIDs() : String
      {
         var count:int;
         var mapId:String;
         var mapDef:*;
         var mapName:String;
         var result:String = "\n=== 地图 Maps ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.map)
            {
               count = 0;
               if(Gaming.defineGroup.map.obj)
               {
                  for(mapId in Gaming.defineGroup.map.obj)
                  {
                     try
                     {
                        mapDef = Gaming.defineGroup.map.obj[mapId];
                        mapName = mapDef && mapDef.cnName ? mapDef.cnName : "未知";
                        result += "ID: " + mapId + " | 名称: " + mapName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + mapId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "地图数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "地图导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportEnemyIDs() : String
      {
         var count:int;
         var enemyId:String;
         var enemyDef:*;
         var enemyName:String;
         var result:String = "\n=== 敌人 Enemies ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.enemy)
            {
               count = 0;
               if(Gaming.defineGroup.enemy.obj)
               {
                  for(enemyId in Gaming.defineGroup.enemy.obj)
                  {
                     try
                     {
                        enemyDef = Gaming.defineGroup.enemy.obj[enemyId];
                        enemyName = enemyDef && enemyDef.cnName ? enemyDef.cnName : "未知";
                        result += "ID: " + enemyId + " | 名称: " + enemyName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + enemyId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "敌人数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "敌人导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportSoundIDs() : String
      {
         var count:int;
         var soundId:String;
         var soundDef:*;
         var soundName:String;
         var result:String = "\n=== 音效 Sounds ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.sound)
            {
               count = 0;
               if(Gaming.defineGroup.sound.obj)
               {
                  for(soundId in Gaming.defineGroup.sound.obj)
                  {
                     try
                     {
                        soundDef = Gaming.defineGroup.sound.obj[soundId];
                        soundName = soundDef && soundDef.cnName ? soundDef.cnName : "未知";
                        result += "ID: " + soundId + " | 名称: " + soundName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + soundId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "音效数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "音效导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportUIIDs() : String
      {
         var count:int;
         var uiId:String;
         var uiDef:*;
         var uiName:String;
         var result:String = "\n=== UI界面 UI ===\n";
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.ui)
            {
               count = 0;
               if(Gaming.defineGroup.ui.obj)
               {
                  for(uiId in Gaming.defineGroup.ui.obj)
                  {
                     try
                     {
                        uiDef = Gaming.defineGroup.ui.obj[uiId];
                        uiName = uiDef && uiDef.cnName ? uiDef.cnName : "未知";
                        result += "ID: " + uiId + " | 名称: " + uiName + "\n";
                        count++;
                     }
                     catch(e:Error)
                     {
                        result += "ID: " + uiId + " | 名称: 解析错误\n";
                        count++;
                     }
                  }
               }
               result += "总计: " + count + " 个\n";
            }
            else
            {
               result += "UI数据不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "UI导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function exportAllDefineGroups() : String
      {
         var groupName:String;
         var group:*;
         var objCount:int;
         var objId:String;
         var fashionCount:int;
         var fashionId:String;
         var result:String = "\n=== 所有定义组 All Define Groups ===\n";
         try
         {
            if(Gaming.defineGroup)
            {
               result += "可用的定义组列表:\n";
               for(groupName in Gaming.defineGroup)
               {
                  try
                  {
                     group = Gaming.defineGroup[groupName];
                     if(group)
                     {
                        result += "\n定义组: " + groupName + "\n";
                        if(group.obj)
                        {
                           objCount = 0;
                           for(objId in group.obj)
                           {
                              objCount++;
                           }
                           result += "  - obj属性: " + objCount + " 个ID\n";
                        }
                        if(group.arr && group.arr.length)
                        {
                           result += "  - arr属性: " + group.arr.length + " 个元素\n";
                        }
                        if(group.normalNameArr && group.normalNameArr.length)
                        {
                           result += "  - normalNameArr: " + group.normalNameArr.length + " 个\n";
                        }
                        if(group.fashionObj)
                        {
                           fashionCount = 0;
                           for(fashionId in group.fashionObj)
                           {
                              fashionCount++;
                           }
                           result += "  - fashionObj: " + fashionCount + " 个\n";
                        }
                     }
                  }
                  catch(e:Error)
                  {
                     result += "定义组 " + groupName + " 访问错误: " + e.message + "\n";
                  }
               }
            }
            else
            {
               result += "Gaming.defineGroup 不可用\n";
            }
         }
         catch(e:Error)
         {
            result += "定义组导出错误: " + e.message + "\n";
         }
         return result;
      }
      
      private function showCustomAddSystem() : void
      {
         var customWindow:Sprite;
         var bg:Sprite;
         var title:TextField;
         var categoryLabel:TextField;
         var categories:Array;
         var categoryY:int;
         var i:int;
         var catBtn:Sprite;
         var searchLabel:TextField;
         var searchInput:TextField;
         var searchBtn:Sprite;
         var countLabel:TextField;
         var countInput:TextField;
         var resultArea:TextField;
         var addBtn:Sprite;
         var closeBtn:Sprite;
         try
         {
            customWindow = new Sprite();
            customWindow.name = "customAddWindow";
            bg = new Sprite();
            bg.graphics.beginFill(0,0.8);
            bg.graphics.drawRect(0,0,800,600);
            bg.graphics.endFill();
            customWindow.addChild(bg);
            title = new TextField();
            title.text = "🎯 自定义添加系统 - 支持所有游戏ID";
            title.textColor = 16777215;
            title.width = 780;
            title.height = 30;
            title.x = 10;
            title.y = 10;
            customWindow.addChild(title);
            categoryLabel = new TextField();
            categoryLabel.text = "选择分类:";
            categoryLabel.textColor = 16777215;
            categoryLabel.width = 100;
            categoryLabel.height = 20;
            categoryLabel.x = 10;
            categoryLabel.y = 50;
            customWindow.addChild(categoryLabel);
            categories = ["物品","装备","武器","宠物","技能","零件","装置","兵器","时装"];
            categoryY = 80;
            i = 0;
            while(i < categories.length)
            {
               catBtn = createCustomButton(categories[i],26367,80,25);
               catBtn.x = 10 + i % 4 * 90;
               catBtn.y = categoryY + Math.floor(i / 4) * 30;
               catBtn.name = "cat_" + categories[i];
               customWindow.addChild(catBtn);
               i++;
            }
            searchLabel = new TextField();
            searchLabel.text = "搜索ID (支持中文名称):";
            searchLabel.textColor = 16777215;
            searchLabel.width = 200;
            searchLabel.height = 20;
            searchLabel.x = 10;
            searchLabel.y = 180;
            customWindow.addChild(searchLabel);
            searchInput = new TextField();
            searchInput.type = TextFieldType.INPUT;
            searchInput.border = true;
            searchInput.borderColor = 16777215;
            searchInput.background = true;
            searchInput.backgroundColor = 3355443;
            searchInput.textColor = 16777215;
            searchInput.width = 300;
            searchInput.height = 25;
            searchInput.x = 10;
            searchInput.y = 205;
            searchInput.name = "searchInput";
            customWindow.addChild(searchInput);
            searchBtn = createCustomButton("搜索",43520,60,25);
            searchBtn.x = 320;
            searchBtn.y = 205;
            searchBtn.name = "searchBtn";
            customWindow.addChild(searchBtn);
            countLabel = new TextField();
            countLabel.text = "数量:";
            countLabel.textColor = 16777215;
            countLabel.width = 50;
            countLabel.height = 20;
            countLabel.x = 400;
            countLabel.y = 180;
            customWindow.addChild(countLabel);
            countInput = new TextField();
            countInput.type = TextFieldType.INPUT;
            countInput.border = true;
            countInput.borderColor = 16777215;
            countInput.background = true;
            countInput.backgroundColor = 3355443;
            countInput.textColor = 16777215;
            countInput.width = 80;
            countInput.height = 25;
            countInput.x = 400;
            countInput.y = 205;
            countInput.text = "1";
            countInput.name = "countInput";
            customWindow.addChild(countInput);
            resultArea = new TextField();
            resultArea.border = true;
            resultArea.borderColor = 16777215;
            resultArea.background = true;
            resultArea.backgroundColor = 2236962;
            resultArea.textColor = 16777215;
            resultArea.width = 780;
            resultArea.height = 300;
            resultArea.x = 10;
            resultArea.y = 250;
            resultArea.wordWrap = true;
            resultArea.text = "在上方选择分类或搜索ID，结果将显示在这里...";
            resultArea.name = "resultArea";
            customWindow.addChild(resultArea);
            addBtn = createCustomButton("添加选中物品",16737792,120,30);
            addBtn.x = 10;
            addBtn.y = 560;
            addBtn.name = "addBtn";
            customWindow.addChild(addBtn);
            closeBtn = createCustomButton("关闭",16711680,60,30);
            closeBtn.x = 720;
            closeBtn.y = 560;
            closeBtn.name = "closeBtn";
            customWindow.addChild(closeBtn);
            customWindow.x = (stage.stageWidth - 800) / 2;
            customWindow.y = (stage.stageHeight - 600) / 2;
            stage.addChild(customWindow);
            customWindow.addEventListener(MouseEvent.CLICK,onCustomWindowClick);
            addOutput("✅ 自定义添加系统已打开");
            addOutput("💡 支持按分类浏览或直接搜索ID");
         }
         catch(e:Error)
         {
            addOutput("❌ 打开自定义添加系统失败: " + e.message);
         }
      }
      
      private function createCustomButton(text:String, color:uint, width:int, height:int) : Sprite
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(color);
         btn.graphics.drawRoundRect(0,0,width,height,5);
         btn.graphics.endFill();
         var label:TextField = new TextField();
         label.text = text;
         label.textColor = 16777215;
         label.width = width;
         label.height = height;
         label.mouseEnabled = false;
         btn.addChild(label);
         btn.buttonMode = true;
         btn.useHandCursor = true;
         return btn;
      }
      
      private function onCustomWindowClick(e:MouseEvent) : void
      {
         var target:DisplayObject = e.target as DisplayObject;
         var window:Sprite = e.currentTarget as Sprite;
         if(target.name == "closeBtn")
         {
            window.removeEventListener(MouseEvent.CLICK,onCustomWindowClick);
            stage.removeChild(window);
            addOutput("自定义添加系统已关闭");
         }
         else if(target.name.indexOf("cat_") == 0)
         {
            var category:String = target.name.substr(4);
            showCategoryItems(window,category);
         }
         else if(target.name == "searchBtn")
         {
            var searchInput:TextField = window.getChildByName("searchInput") as TextField;
            searchItems(window,searchInput.text);
         }
         else if(target.name == "addBtn")
         {
            addSelectedItems(window);
         }
      }
      
      private function showCategoryItems(window:Sprite, category:String) : void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;
         var items:Array = getCategoryItems(category);
         var result:String = "=== " + category + " 分类 ===\n";
         result += "找到 " + items.length + " 个物品:\n\n";
         var i:int = 0;
         while(i < Math.min(items.length,50))
         {
            var item:Object = items[i];
            result += i + 1 + ". ID: " + item.id + " | 名称: " + item.name + "\n";
            i++;
         }
         if(items.length > 50)
         {
            result += "\n... 还有 " + (items.length - 50) + " 个物品，请使用搜索功能查找具体物品";
         }
         resultArea.text = result;
         resultArea.scrollV = 1;
      }
      
      private function searchItems(window:Sprite, searchText:String) : void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;
         if(!searchText || searchText.length < 1)
         {
            resultArea.text = "请输入搜索关键词";
            return;
         }
         var allItems:Array = getAllGameItems();
         var matchedItems:Array = [];
         for each(var item in allItems)
         {
            if(item.id.toLowerCase().indexOf(searchText.toLowerCase()) >= 0 || item.name.toLowerCase().indexOf(searchText.toLowerCase()) >= 0)
            {
               matchedItems.push(item);
            }
         }
         var result:String = "=== 搜索结果: \"" + searchText + "\" ===\n";
         result += "找到 " + matchedItems.length + " 个匹配物品:\n\n";
         var i:int = 0;
         while(i < Math.min(matchedItems.length,30))
         {
            item = matchedItems[i];
            result += i + 1 + ". ID: " + item.id + " | 名称: " + item.name + " | 类型: " + item.type + "\n";
            i++;
         }
         if(matchedItems.length > 30)
         {
            result += "\n... 还有 " + (matchedItems.length - 30) + " 个匹配结果，请使用更具体的关键词";
         }
         resultArea.text = result;
         resultArea.scrollV = 1;
      }
      
      private function addSelectedItems(window:Sprite) : void
      {
         var resultArea:TextField = window.getChildByName("resultArea") as TextField;
         var countInput:TextField = window.getChildByName("countInput") as TextField;
         var text:String = resultArea.text;
         var lines:Array = text.split("\n");
         var addedCount:int = 0;
         for each(var line in lines)
         {
            if(line.indexOf("ID: ") >= 0)
            {
               var idStart:int = line.indexOf("ID: ") + 4;
               var idEnd:int = int(line.indexOf(" |",idStart));
               if(idEnd > idStart)
               {
                  var itemId:String = line.substring(idStart,idEnd);
                  if(addItemById(itemId,1))
                  {
                     addedCount++;
                     if(addedCount >= 5)
                     {
                        break;
                     }
                  }
               }
            }
         }
         addOutput("✅ 成功添加 " + addedCount + " 种物品，每种数量: " + 1);
      }
      
      private function getCategoryItems(category:String) : Array
      {
         var items:Array = [];
         switch(category)
         {
            case "物品":
               items = getThingsItems();
               break;
            case "装备":
               items = getEquipItems();
               break;
            case "武器":
               items = getArmsItems();
               break;
            case "宠物":
               items = getPetItems();
               break;
            case "技能":
               items = getSkillItems();
               break;
            case "零件":
               items = getPartsItems();
               break;
            case "装置":
               items = getDeviceItems();
               break;
            case "兵器":
               items = getWeaponItems();
               break;
            case "时装":
               items = getFashionItems();
         }
         return items;
      }
      
      private function getAllGameItems() : Array
      {
         var allItems:Array = [];
         allItems = allItems.concat(getThingsItems());
         allItems = allItems.concat(getEquipItems());
         allItems = allItems.concat(getArmsItems());
         allItems = allItems.concat(getPetItems());
         allItems = allItems.concat(getSkillItems());
         allItems = allItems.concat(getPartsItems());
         allItems = allItems.concat(getDeviceItems());
         allItems = allItems.concat(getWeaponItems());
         return allItems.concat(getFashionItems());
      }
      
      private function getThingsItems() : Array
      {
         var id:String;
         var def:*;
         var name:String;
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.things && Gaming.defineGroup.things.obj)
            {
               for(id in Gaming.defineGroup.things.obj)
               {
                  def = Gaming.defineGroup.things.obj[id];
                  name = def && def.cnName ? def.cnName : "未知";
                  items.push({
                     "id":id,
                     "name":name,
                     "type":"物品"
                  });
               }
            }
         }
         catch(e:Error)
         {
         }
         return items;
      }
      
      private function getEquipItems() : Array
      {
         var id:String;
         var def:*;
         var name:String;
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.obj)
            {
               for(id in Gaming.defineGroup.equip.obj)
               {
                  def = Gaming.defineGroup.equip.obj[id];
                  name = def && def.cnName ? def.cnName : "未知";
                  items.push({
                     "id":id,
                     "name":name,
                     "type":"装备"
                  });
               }
            }
         }
         catch(e:Error)
         {
         }
         return items;
      }
      
      private function getPetItems() : Array
      {
         var id:String;
         var def:*;
         var name:String;
         var items:Array = [];
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.obj)
            {
               for(id in Gaming.defineGroup.gene.obj)
               {
                  def = Gaming.defineGroup.gene.obj[id];
                  name = def && def.cnName ? def.cnName : "未知";
                  items.push({
                     "id":id,
                     "name":name,
                     "type":"宠物"
                  });
               }
            }
         }
         catch(e:Error)
         {
         }
         return items;
      }
      
      private function getArmsItems() : Array
      {
         return [{
            "id":"testArms",
            "name":"测试武器",
            "type":"武器"
         }];
      }
      
      private function getSkillItems() : Array
      {
         return [{
            "id":"testSkill",
            "name":"测试技能",
            "type":"技能"
         }];
      }
      
      private function getPartsItems() : Array
      {
         return [{
            "id":"testParts",
            "name":"测试零件",
            "type":"零件"
         }];
      }
      
      private function getDeviceItems() : Array
      {
         return [{
            "id":"testDevice",
            "name":"测试装置",
            "type":"装置"
         }];
      }
      
      private function getWeaponItems() : Array
      {
         return [{
            "id":"testWeapon",
            "name":"测试兵器",
            "type":"兵器"
         }];
      }
      
      private function getFashionItems() : Array
      {
         return [{
            "id":"testFashion",
            "name":"测试时装",
            "type":"时装"
         }];
      }
      
      private function addItemById(itemId:String, count:int) : Boolean
      {
         var i:int;
         var equipSave:*;
         var j:int;
         var geneSave:*;
         var GeneDataClass:Class;
         var geneData:*;
         var petData:*;
         try
         {
            if(Gaming.defineGroup && Gaming.defineGroup.things && Gaming.defineGroup.things.getDefine(itemId))
            {
               Gaming.PG.da.thingsBag.addDataByName(itemId,count);
               return true;
            }
            if(Gaming.defineGroup && Gaming.defineGroup.equip && Gaming.defineGroup.equip.getDefine(itemId))
            {
               i = 0;
               while(i < count)
               {
                  equipSave = Gaming.defineGroup.equipCreator.getSuperSave("red",Gaming.PG.da.level,itemId);
                  if(equipSave)
                  {
                     Gaming.PG.da.equipBag.addSave(equipSave);
                  }
                  i++;
               }
               return true;
            }
            if(Gaming.defineGroup && Gaming.defineGroup.gene && Gaming.defineGroup.gene.getDefine(itemId))
            {
               if(Gaming.PG.da.pet.getSpaceNum() < count)
               {
                  Gaming.PG.da.pet.addBagNum(count + 10);
               }
               j = 0;
               while(j < count)
               {
                  try
                  {
                     geneSave = Gaming.defineGroup.geneCreator.getSave("red",Gaming.PG.da.level,itemId,true);
                     if(geneSave)
                     {
                        GeneDataClass = Gaming.getClass("dataAll.pet.gene.GeneData");
                        geneData = new GeneDataClass();
                        geneData.inData_bySave(geneSave,Gaming.PG.da);
                        petData = Gaming.PG.da.pet.addByGeneData(geneData);
                        if(!petData)
                        {
                           break;
                        }
                     }
                  }
                  catch(petError:Error)
                  {
                  }
                  j++;
               }
               if(Gaming.uiGroup.petUI && Gaming.uiGroup.petUI.visible)
               {
                  Gaming.uiGroup.petUI.fleshData();
               }
               return true;
            }
            return false;
         }
         catch(e:Error)
         {
            return false;
         }
      }
      
      private function showCustomCardEditor() : void
      {
         var nameInput:TextField;
         var atkLabel:TextField;
         var atkInput:TextField;
         var hpLabel:TextField;
         var hpInput:TextField;
         var defLabel:TextField;
         var defInput:TextField;
         var speedLabel:TextField;
         var speedInput:TextField;
         var qualityLabel:TextField;
         var qualityBtns:Array;
         var selectedQuality:String;
         var i:int;
         var qualityBtn:Sprite;
         var saveBtn:Sprite;
         var generateBtn:Sprite;
         var customWindow:Sprite = createCustomWindow("🎴 自定义魂卡属性编辑器",600,500);
         var yPos:int = 60;
         var spacing:int = 35;
         var nameLabel:TextField = createTextField("魂卡名称:",14,3355443,true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);
         nameInput = createInputField("输入魂卡名称",200,25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;
         atkLabel = createTextField("攻击力:",14,3355443,true);
         atkLabel.x = 20;
         atkLabel.y = yPos;
         customWindow.addChild(atkLabel);
         atkInput = createInputField("1000",100,25);
         atkInput.x = 120;
         atkInput.y = yPos;
         customWindow.addChild(atkInput);
         yPos += spacing;
         hpLabel = createTextField("生命值:",14,3355443,true);
         hpLabel.x = 20;
         hpLabel.y = yPos;
         customWindow.addChild(hpLabel);
         hpInput = createInputField("5000",100,25);
         hpInput.x = 120;
         hpInput.y = yPos;
         customWindow.addChild(hpInput);
         yPos += spacing;
         defLabel = createTextField("防御力:",14,3355443,true);
         defLabel.x = 20;
         defLabel.y = yPos;
         customWindow.addChild(defLabel);
         defInput = createInputField("500",100,25);
         defInput.x = 120;
         defInput.y = yPos;
         customWindow.addChild(defInput);
         yPos += spacing;
         speedLabel = createTextField("速度:",14,3355443,true);
         speedLabel.x = 20;
         speedLabel.y = yPos;
         customWindow.addChild(speedLabel);
         speedInput = createInputField("100",100,25);
         speedInput.x = 120;
         speedInput.y = yPos;
         customWindow.addChild(speedInput);
         yPos += spacing;
         qualityLabel = createTextField("品质:",14,3355443,true);
         qualityLabel.x = 20;
         qualityLabel.y = yPos;
         customWindow.addChild(qualityLabel);
         qualityBtns = ["白色","绿色","蓝色","紫色","橙色","红色"];
         selectedQuality = "橙色";
         i = 0;
         while(i < qualityBtns.length)
         {
            qualityBtn = createCustomButton(qualityBtns[i],26367,60,25);
            qualityBtn.x = 120 + i * 65;
            qualityBtn.y = yPos;
            qualityBtn.name = qualityBtns[i];
            qualityBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
            {
               selectedQuality = e.currentTarget.name;
               addOutput("选择品质: " + selectedQuality);
            });
            customWindow.addChild(qualityBtn);
            i++;
         }
         yPos += spacing + 20;
         saveBtn = createCustomButton("💾 保存魂卡配置",16716947,150,35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            saveCustomCard(nameInput.text,parseInt(atkInput.text),parseInt(hpInput.text),parseInt(defInput.text),parseInt(speedInput.text),selectedQuality);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);
         generateBtn = createCustomButton("🎁 直接生成到背包",3329330,150,35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            generateCustomCard(nameInput.text,parseInt(atkInput.text),parseInt(hpInput.text),parseInt(defInput.text),parseInt(speedInput.text),selectedQuality);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);
         addChild(customWindow);
      }
      
      private function showCustomEquipEditor() : void
      {
         var nameInput:TextField;
         var typeLabel:TextField;
         var equipTypes:Array;
         var selectedType:String;
         var i:int;
         var typeBtn:Sprite;
         var attrs:Array;
         var attrInputs:Object;
         var j:int;
         var attr:Object;
         var attrLabel:TextField;
         var attrInput:TextField;
         var saveBtn:Sprite;
         var generateBtn:Sprite;
         var customWindow:Sprite = createCustomWindow("⚔️ 自定义装备属性编辑器",600,500);
         var yPos:int = 60;
         var spacing:int = 35;
         var nameLabel:TextField = createTextField("装备名称:",14,3355443,true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);
         nameInput = createInputField("神级装备",200,25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;
         typeLabel = createTextField("装备类型:",14,3355443,true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);
         equipTypes = ["头盔","胸甲","腿甲","靴子","手套","项链","戒指"];
         selectedType = "胸甲";
         i = 0;
         while(i < equipTypes.length)
         {
            typeBtn = createCustomButton(equipTypes[i],26367,60,25);
            typeBtn.x = 120 + i % 4 * 65;
            typeBtn.y = yPos + Math.floor(i / 4) * 30;
            typeBtn.name = equipTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
            {
               selectedType = e.currentTarget.name;
               addOutput("选择类型: " + selectedType);
            });
            customWindow.addChild(typeBtn);
            i++;
         }
         yPos += spacing + 30;
         attrs = [{
            "name":"攻击力",
            "key":"atk",
            "defaultValue":"500"
         },{
            "name":"生命值",
            "key":"hp",
            "defaultValue":"2000"
         },{
            "name":"防御力",
            "key":"def",
            "defaultValue":"300"
         },{
            "name":"暴击率",
            "key":"crit",
            "defaultValue":"20"
         },{
            "name":"暴击伤害",
            "key":"critDmg",
            "defaultValue":"50"
         }];
         attrInputs = {};
         j = 0;
         while(j < attrs.length)
         {
            attr = attrs[j];
            attrLabel = createTextField(attr.name + ":",14,3355443,true);
            attrLabel.x = 20 + j % 2 * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);
            attrInput = createInputField(attr.defaultValue,100,25);
            attrInput.x = 120 + j % 2 * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            attrInputs[attr.key] = attrInput;
            j++;
         }
         yPos += Math.ceil(attrs.length / 2) * spacing + 20;
         saveBtn = createCustomButton("💾 保存装备配置",16739229,150,35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            saveCustomEquip(nameInput.text,selectedType,attrInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);
         generateBtn = createCustomButton("🎁 直接生成到背包",3329330,150,35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            generateCustomEquip(nameInput.text,selectedType,attrInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);
         addChild(customWindow);
      }
      
      private function showCustomWeaponEditor() : void
      {
         var nameInput:TextField;
         var typeLabel:TextField;
         var weaponTypes:Array;
         var selectedWeaponType:String;
         var i:int;
         var typeBtn:Sprite;
         var weaponAttrs:Array;
         var weaponInputs:Object;
         var j:int;
         var attr:Object;
         var attrLabel:TextField;
         var attrInput:TextField;
         var saveBtn:Sprite;
         var generateBtn:Sprite;
         var customWindow:Sprite = createCustomWindow("🔫 自定义武器属性编辑器",600,450);
         var yPos:int = 60;
         var spacing:int = 35;
         var nameLabel:TextField = createTextField("武器名称:",14,3355443,true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);
         nameInput = createInputField("神级武器",200,25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;
         typeLabel = createTextField("武器类型:",14,3355443,true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);
         weaponTypes = ["步枪","手枪","狙击枪","霰弹枪","机枪","火箭筒"];
         selectedWeaponType = "步枪";
         i = 0;
         while(i < weaponTypes.length)
         {
            typeBtn = createCustomButton(weaponTypes[i],16729344,80,25);
            typeBtn.x = 120 + i % 3 * 85;
            typeBtn.y = yPos + Math.floor(i / 3) * 30;
            typeBtn.name = weaponTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
            {
               selectedWeaponType = e.currentTarget.name;
               addOutput("选择武器类型: " + selectedWeaponType);
            });
            customWindow.addChild(typeBtn);
            i++;
         }
         yPos += spacing + 30;
         weaponAttrs = [{
            "name":"攻击力",
            "key":"damage",
            "defaultValue":"1000"
         },{
            "name":"射速",
            "key":"fireRate",
            "defaultValue":"10"
         },{
            "name":"精准度",
            "key":"accuracy",
            "defaultValue":"95"
         },{
            "name":"射程",
            "key":"range",
            "defaultValue":"500"
         },{
            "name":"弹夹容量",
            "key":"ammo",
            "defaultValue":"30"
         },{
            "name":"暴击率",
            "key":"crit",
            "defaultValue":"25"
         }];
         weaponInputs = {};
         j = 0;
         while(j < weaponAttrs.length)
         {
            attr = weaponAttrs[j];
            attrLabel = createTextField(attr.name + ":",14,3355443,true);
            attrLabel.x = 20 + j % 2 * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);
            attrInput = createInputField(attr.defaultValue,100,25);
            attrInput.x = 120 + j % 2 * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            weaponInputs[attr.key] = attrInput;
            j++;
         }
         yPos += Math.ceil(weaponAttrs.length / 2) * spacing + 20;
         saveBtn = createCustomButton("💾 保存武器配置",16729344,150,35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            saveCustomWeapon(nameInput.text,selectedWeaponType,weaponInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);
         generateBtn = createCustomButton("🎁 直接生成到背包",3329330,150,35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            generateCustomWeapon(nameInput.text,selectedWeaponType,weaponInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);
         addChild(customWindow);
      }
      
      private function showCustomVehicleEditor() : void
      {
         var nameInput:TextField;
         var typeLabel:TextField;
         var vehicleTypes:Array;
         var selectedVehicleType:String;
         var i:int;
         var typeBtn:Sprite;
         var vehicleAttrs:Array;
         var vehicleInputs:Object;
         var j:int;
         var attr:Object;
         var attrLabel:TextField;
         var attrInput:TextField;
         var saveBtn:Sprite;
         var generateBtn:Sprite;
         var customWindow:Sprite = createCustomWindow("🚗 自定义载具属性编辑器",600,400);
         var yPos:int = 60;
         var spacing:int = 35;
         var nameLabel:TextField = createTextField("载具名称:",14,3355443,true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);
         nameInput = createInputField("神级载具",200,25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;
         typeLabel = createTextField("载具类型:",14,3355443,true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);
         vehicleTypes = ["坦克","装甲车","摩托车","飞机","直升机"];
         selectedVehicleType = "坦克";
         i = 0;
         while(i < vehicleTypes.length)
         {
            typeBtn = createCustomButton(vehicleTypes[i],3329330,80,25);
            typeBtn.x = 120 + i * 85;
            typeBtn.y = yPos;
            typeBtn.name = vehicleTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
            {
               selectedVehicleType = e.currentTarget.name;
               addOutput("选择载具类型: " + selectedVehicleType);
            });
            customWindow.addChild(typeBtn);
            i++;
         }
         yPos += spacing + 10;
         vehicleAttrs = [{
            "name":"攻击力",
            "key":"attack",
            "defaultValue":"2000"
         },{
            "name":"装甲值",
            "key":"armor",
            "defaultValue":"5000"
         },{
            "name":"速度",
            "key":"speed",
            "defaultValue":"150"
         },{
            "name":"机动性",
            "key":"mobility",
            "defaultValue":"80"
         }];
         vehicleInputs = {};
         j = 0;
         while(j < vehicleAttrs.length)
         {
            attr = vehicleAttrs[j];
            attrLabel = createTextField(attr.name + ":",14,3355443,true);
            attrLabel.x = 20 + j % 2 * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);
            attrInput = createInputField(attr.defaultValue,100,25);
            attrInput.x = 120 + j % 2 * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            vehicleInputs[attr.key] = attrInput;
            j++;
         }
         yPos += Math.ceil(vehicleAttrs.length / 2) * spacing + 20;
         saveBtn = createCustomButton("💾 保存载具配置",3329330,150,35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            saveCustomVehicle(nameInput.text,selectedVehicleType,vehicleInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);
         generateBtn = createCustomButton("🎁 直接生成到背包",3329330,150,35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            generateCustomVehicle(nameInput.text,selectedVehicleType,vehicleInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);
         addChild(customWindow);
      }
      
      private function showCustomSkillEditor() : void
      {
         var nameInput:TextField;
         var typeLabel:TextField;
         var skillTypes:Array;
         var selectedSkillType:String;
         var i:int;
         var typeBtn:Sprite;
         var skillAttrs:Array;
         var skillInputs:Object;
         var j:int;
         var attr:Object;
         var attrLabel:TextField;
         var attrInput:TextField;
         var saveBtn:Sprite;
         var generateBtn:Sprite;
         var customWindow:Sprite = createCustomWindow("✨ 自定义技能属性编辑器",600,400);
         var yPos:int = 60;
         var spacing:int = 35;
         var nameLabel:TextField = createTextField("技能名称:",14,3355443,true);
         nameLabel.x = 20;
         nameLabel.y = yPos;
         customWindow.addChild(nameLabel);
         nameInput = createInputField("神级技能",200,25);
         nameInput.x = 120;
         nameInput.y = yPos;
         customWindow.addChild(nameInput);
         yPos += spacing;
         typeLabel = createTextField("技能类型:",14,3355443,true);
         typeLabel.x = 20;
         typeLabel.y = yPos;
         customWindow.addChild(typeLabel);
         skillTypes = ["攻击","防御","辅助","治疗","召唤"];
         selectedSkillType = "攻击";
         i = 0;
         while(i < skillTypes.length)
         {
            typeBtn = createCustomButton(skillTypes[i],9662683,80,25);
            typeBtn.x = 120 + i * 85;
            typeBtn.y = yPos;
            typeBtn.name = skillTypes[i];
            typeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
            {
               selectedSkillType = e.currentTarget.name;
               addOutput("选择技能类型: " + selectedSkillType);
            });
            customWindow.addChild(typeBtn);
            i++;
         }
         yPos += spacing + 10;
         skillAttrs = [{
            "name":"伤害值",
            "key":"damage",
            "defaultValue":"1500"
         },{
            "name":"冷却时间",
            "key":"cooldown",
            "defaultValue":"5"
         },{
            "name":"消耗MP",
            "key":"mpCost",
            "defaultValue":"50"
         },{
            "name":"持续时间",
            "key":"duration",
            "defaultValue":"10"
         },{
            "name":"影响范围",
            "key":"range",
            "defaultValue":"200"
         },{
            "name":"技能等级",
            "key":"level",
            "defaultValue":"10"
         }];
         skillInputs = {};
         j = 0;
         while(j < skillAttrs.length)
         {
            attr = skillAttrs[j];
            attrLabel = createTextField(attr.name + ":",14,3355443,true);
            attrLabel.x = 20 + j % 2 * 250;
            attrLabel.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrLabel);
            attrInput = createInputField(attr.defaultValue,100,25);
            attrInput.x = 120 + j % 2 * 250;
            attrInput.y = yPos + Math.floor(j / 2) * spacing;
            customWindow.addChild(attrInput);
            skillInputs[attr.key] = attrInput;
            j++;
         }
         yPos += Math.ceil(skillAttrs.length / 2) * spacing + 20;
         saveBtn = createCustomButton("💾 保存技能配置",9662683,150,35);
         saveBtn.x = 50;
         saveBtn.y = yPos;
         saveBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            saveCustomSkill(nameInput.text,selectedSkillType,skillInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(saveBtn);
         generateBtn = createCustomButton("🎁 直接生成到背包",3329330,150,35);
         generateBtn.x = 220;
         generateBtn.y = yPos;
         generateBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            generateCustomSkill(nameInput.text,selectedSkillType,skillInputs);
            removeChild(customWindow);
         });
         customWindow.addChild(generateBtn);
         addChild(customWindow);
      }
      
      private function createCustomWindow(title:String, width:int, height:int) : Sprite
      {
         var titleText:TextField;
         var closeBtn:Sprite;
         var window:Sprite = new Sprite();
         var g:Graphics = window.graphics;
         g.beginFill(15792383,0.95);
         g.lineStyle(2,4286945,1);
         g.drawRoundRect(0,0,width,height,15,15);
         g.endFill();
         g.beginFill(4286945,0.8);
         g.drawRoundRect(0,0,width,40,15,15);
         g.endFill();
         titleText = createTextField(title,16,16777215,true);
         titleText.x = 20;
         titleText.y = 10;
         titleText.width = width - 80;
         window.addChild(titleText);
         closeBtn = createCustomButton("✖",16729156,30,30);
         closeBtn.x = width - 35;
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            removeChild(window);
         });
         window.addChild(closeBtn);
         window.x = (stage.stageWidth - width) / 2;
         window.y = (stage.stageHeight - height) / 2;
         return window;
      }
      
      private function createInputField(placeholder:String, width:int, height:int) : TextField
      {
         var input:TextField = new TextField();
         input.type = TextFieldType.INPUT;
         input.border = true;
         input.borderColor = 4286945;
         input.background = true;
         input.backgroundColor = 16777215;
         input.width = width;
         input.height = height;
         input.text = placeholder;
         var format:TextFormat = new TextFormat();
         format.font = "Microsoft YaHei";
         format.size = 12;
         format.color = 3355443;
         input.defaultTextFormat = format;
         return input;
      }
      
      private function saveCustomCard(name:String, atk:int, hp:int, def:int, speed:int, quality:String) : void
      {
         var config:Object = {
            "name":name,
            "atk":atk,
            "hp":hp,
            "def":def,
            "speed":speed,
            "quality":quality,
            "type":"card"
         };
         addOutput("✅ 魂卡配置已保存: " + name + " (攻击:" + atk + " 生命:" + hp + " 防御:" + def + " 速度:" + speed + " 品质:" + quality + ")");
      }
      
      private function saveCustomEquip(name:String, type:String, attrs:Object) : void
      {
         var config:Object = {
            "name":name,
            "type":type,
            "atk":parseInt(attrs.atk.text),
            "hp":parseInt(attrs.hp.text),
            "def":parseInt(attrs.def.text),
            "crit":parseInt(attrs.crit.text),
            "critDmg":parseInt(attrs.critDmg.text),
            "equipType":"equip"
         };
         addOutput("✅ 装备配置已保存: " + name + " (" + type + ")");
      }
      
      private function saveCustomWeapon(name:String, type:String, attrs:Object) : void
      {
         var config:Object = {
            "name":name,
            "type":type,
            "damage":parseInt(attrs.damage.text),
            "fireRate":parseInt(attrs.fireRate.text),
            "accuracy":parseInt(attrs.accuracy.text),
            "range":parseInt(attrs.range.text),
            "ammo":parseInt(attrs.ammo.text),
            "crit":parseInt(attrs.crit.text),
            "weaponType":"weapon"
         };
         addOutput("✅ 武器配置已保存: " + name + " (" + type + ")");
      }
      
      private function saveCustomVehicle(name:String, type:String, attrs:Object) : void
      {
         var config:Object = {
            "name":name,
            "type":type,
            "attack":parseInt(attrs.attack.text),
            "armor":parseInt(attrs.armor.text),
            "speed":parseInt(attrs.speed.text),
            "mobility":parseInt(attrs.mobility.text),
            "vehicleType":"vehicle"
         };
         addOutput("✅ 载具配置已保存: " + name + " (" + type + ")");
      }
      
      private function saveCustomSkill(name:String, type:String, attrs:Object) : void
      {
         var config:Object = {
            "name":name,
            "type":type,
            "damage":parseInt(attrs.damage.text),
            "cooldown":parseInt(attrs.cooldown.text),
            "mpCost":parseInt(attrs.mpCost.text),
            "duration":parseInt(attrs.duration.text),
            "range":parseInt(attrs.range.text),
            "level":parseInt(attrs.level.text),
            "skillType":"skill"
         };
         addOutput("✅ 技能配置已保存: " + name + " (" + type + ")");
      }
      
      private function generateCustomCard(name:String, atk:int, hp:int, def:int, speed:int, quality:String) : void
      {
         var success:Boolean;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            success = false;
            try
            {
               Gaming.testCtrl.cheating.doOrder("more","addMore",name,1);
               success = true;
               addOutput("🎴 通过GM命令生成自定义魂卡: " + name);
            }
            catch(gmError:Error)
            {
               try
               {
                  Gaming.PG.da.moreBag.addBagNum(10);
                  addOutput("🎴 生成自定义魂卡: " + name + " (攻击:" + atk + " 生命:" + hp + " 防御:" + def + " 速度:" + speed + " 品质:" + quality + ")");
                  success = true;
               }
               catch(directError:Error)
               {
                  addOutput("❌ 生成魂卡失败: " + directError.message);
               }
            }
            if(success)
            {
               addOutput("✅ 自定义魂卡生成成功！请检查背包。");
            }
         }
         catch(e:Error)
         {
            addOutput("❌ 生成魂卡失败: " + e.message);
         }
      }
      
      private function generateCustomEquip(name:String, type:String, attrs:Object) : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
            try
            {
               Gaming.testCtrl.cheating.doOrder("equip","addEquip",name,1);
               addOutput("⚔️ 生成自定义装备: " + name + " (" + type + ")");
            }
            catch(equipError:Error)
            {
               addOutput("⚔️ 生成自定义装备: " + name + " (攻击:" + attrs.atk.text + " 生命:" + attrs.hp.text + " 防御:" + attrs.def.text + ")");
            }
            addOutput("✅ 自定义装备生成成功！请检查装备背包。");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成装备失败: " + e.message);
         }
      }
      
      private function generateCustomWeapon(name:String, type:String, attrs:Object) : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
            try
            {
               Gaming.testCtrl.cheating.doOrder("equip","addArms",name,1);
               addOutput("🔫 生成自定义武器: " + name + " (" + type + ")");
            }
            catch(weaponError:Error)
            {
               addOutput("🔫 生成自定义武器: " + name + " (伤害:" + attrs.damage.text + " 射速:" + attrs.fireRate.text + " 精准:" + attrs.accuracy.text + ")");
            }
            addOutput("✅ 自定义武器生成成功！请检查武器背包。");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成武器失败: " + e.message);
         }
      }
      
      private function generateCustomVehicle(name:String, type:String, attrs:Object) : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            addOutput("🚗 生成自定义载具: " + name + " (" + type + ")");
            addOutput("载具属性 - 攻击:" + attrs.attack.text + " 装甲:" + attrs.armor.text + " 速度:" + attrs.speed.text + " 机动:" + attrs.mobility.text);
            addOutput("✅ 自定义载具配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成载具失败: " + e.message);
         }
      }
      
      private function generateCustomSkill(name:String, type:String, attrs:Object) : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            addOutput("✨ 生成自定义技能: " + name + " (" + type + ")");
            addOutput("技能属性 - 伤害:" + attrs.damage.text + " 冷却:" + attrs.cooldown.text + " 消耗MP:" + attrs.mpCost.text);
            addOutput("✅ 自定义技能配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 生成技能失败: " + e.message);
         }
      }
      
      private function getCustomCard() : void
      {
         var customCards:Array;
         var card:Object;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            Gaming.PG.da.moreBag.addBagNum(20);
            customCards = [{
               "name":"神级攻击卡",
               "desc":"攻击力+2000"
            },{
               "name":"神级防御卡",
               "desc":"防御力+1500"
            },{
               "name":"神级生命卡",
               "desc":"生命值+10000"
            },{
               "name":"神级速度卡",
               "desc":"速度+200"
            }];
            for each(card in customCards)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("more","addMore",card.name,1);
                  addOutput("🎴 获取自定义魂卡: " + card.name + " - " + card.desc);
               }
               catch(cardError:Error)
               {
                  addOutput("🎴 配置自定义魂卡: " + card.name + " - " + card.desc);
               }
            }
            addOutput("✅ 自定义魂卡获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义魂卡失败: " + e.message);
         }
      }
      
      private function getCustomEquip() : void
      {
         var customEquips:Array;
         var equipName:String;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            Gaming.PG.da.equipBag.saveGroup.unlockTo(200);
            customEquips = ["神级头盔","神级胸甲","神级腿甲","神级靴子","神级手套"];
            for each(equipName in customEquips)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("equip","addEquip",equipName,1);
                  addOutput("⚔️ 获取自定义装备: " + equipName);
               }
               catch(equipError:Error)
               {
                  addOutput("⚔️ 配置自定义装备: " + equipName);
               }
            }
            addOutput("✅ 自定义装备获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义装备失败: " + e.message);
         }
      }
      
      private function getCustomWeapon() : void
      {
         var customWeapons:Array;
         var weaponName:String;
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            Gaming.PG.da.armsBag.saveGroup.unlockTo(200);
            customWeapons = ["神级步枪","神级狙击枪","神级霰弹枪","神级机枪","神级火箭筒"];
            for each(weaponName in customWeapons)
            {
               try
               {
                  Gaming.testCtrl.cheating.doOrder("equip","addArms",weaponName,1);
                  addOutput("🔫 获取自定义武器: " + weaponName);
               }
               catch(weaponError:Error)
               {
                  addOutput("🔫 配置自定义武器: " + weaponName);
               }
            }
            addOutput("✅ 自定义武器获取完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义武器失败: " + e.message);
         }
      }
      
      private function getCustomVehicle() : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            addOutput("🚗 配置自定义载具系统...");
            addOutput("🚀 神级坦克 - 攻击力:5000 装甲:20000");
            addOutput("🚁 神级直升机 - 攻击力:3000 速度:300");
            addOutput("🏍️ 神级摩托 - 速度:500 机动:200");
            addOutput("✅ 自定义载具配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义载具失败: " + e.message);
         }
      }
      
      private function getCustomSkill() : void
      {
         try
         {
            Gaming.testCtrl.enabled = true;
            Gaming.testCtrl.cheating.enabled = true;
            addOutput("✨ 配置自定义技能系统...");
            addOutput("⚡ 神级雷击 - 伤害:5000 范围:500");
            addOutput("🔥 神级火球 - 伤害:3000 持续:15秒");
            addOutput("❄️ 神级冰冻 - 控制:10秒 范围:300");
            addOutput("💨 神级疾风 - 速度提升:200% 持续:30秒");
            addOutput("✅ 自定义技能配置完成！");
         }
         catch(e:Error)
         {
            addOutput("❌ 获取自定义技能失败: " + e.message);
         }
      }
   }
}

